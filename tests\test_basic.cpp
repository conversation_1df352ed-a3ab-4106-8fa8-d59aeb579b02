/**
 * @file test_basic.cpp
 * @brief 现代C++基础概念学习测试
 * @details 从最简单的例子开始，逐步学习现代C++特性
 */

#include <iostream>
#include <memory>    // 智能指针
#include <string>
#include <vector>

// 我们先创建一个简单的类来演示智能指针
class SimpleConnection {
private:
    std::string name_;
    bool connected_;

public:
    // 构造函数
    SimpleConnection(const std::string& name) 
        : name_(name), connected_(false) {
        std::cout << "创建连接: " << name_ << std::endl;
    }
    
    // 析构函数 - 这里会自动被智能指针调用
    ~SimpleConnection() {
        std::cout << "销毁连接: " << name_ << std::endl;
    }
    
    // 连接方法
    void connect() {
        connected_ = true;
        std::cout << name_ << " 已连接" << std::endl;
    }
    
    // 断开连接
    void disconnect() {
        connected_ = false;
        std::cout << name_ << " 已断开" << std::endl;
    }
    
    // 获取状态
    bool is_connected() const {
        return connected_;
    }
    
    const std::string& get_name() const {
        return name_;
    }
};

// 演示传统指针的问题
void demo_raw_pointer_problems() {
    std::cout << "\n=== 传统指针的问题 ===" << std::endl;
    
    SimpleConnection* conn = new SimpleConnection("传统指针连接");
    conn->connect();
    
    // 问题1: 忘记delete会导致内存泄漏
    // 问题2: 异常发生时可能无法正确释放
    // 问题3: 重复delete会导致程序崩溃
    
    // 这里故意不delete，演示内存泄漏
    std::cout << "忘记delete，内存泄漏！" << std::endl;
}

// 演示unique_ptr的优势
void demo_unique_ptr() {
    std::cout << "\n=== unique_ptr 智能指针 ===" << std::endl;
    
    // 创建unique_ptr - 方法1：使用make_unique（推荐）
    auto conn1 = std::make_unique<SimpleConnection>("unique_ptr连接1");
    conn1->connect();
    
    // 创建unique_ptr - 方法2：直接构造
    std::unique_ptr<SimpleConnection> conn2(new SimpleConnection("unique_ptr连接2"));
    conn2->connect();
    
    std::cout << "函数结束时，unique_ptr会自动释放内存" << std::endl;
    // 不需要手动delete，unique_ptr析构时会自动调用delete
}

// 最简单的shared_ptr演示 - 你来填充这个函数
void demo_shared_ptr() {
    std::cout << "\n=== shared_ptr 演示 ===" << std::endl;
    
    // 任务1: 创建一个shared_ptr，最简单的方式
    // 提示: auto ptr1 = std::make_shared<SimpleConnection>("共享连接");
    
    
    // 任务2: 输出引用计数
    // 提示: std::cout << "引用计数: " << ptr1.use_count() << std::endl;
    
    
    // 任务3: 创建第二个shared_ptr指向同一个对象
    // 提示: auto ptr2 = ptr1;
    
    
    // 任务4: 再次输出引用计数
    
    
}

int main() {
    std::cout << "现代C++学习 - 智能指针基础" << std::endl;
    
    // 演示传统指针的问题
    demo_raw_pointer_problems();
    
    // 演示智能指针的优势
    demo_unique_ptr();
    
    // 新增：演示shared_ptr
    demo_shared_ptr();
    
    std::cout << "\n程序结束" << std::endl;
    return 0;
}