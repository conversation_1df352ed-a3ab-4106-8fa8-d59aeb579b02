/**
 * @file protocol_completion_condition.cpp
 * @brief ASIO自定义完成条件实现
 */

#include "../include/protocol_completion_condition.h"
#include <iostream>
#include <algorithm>

namespace ModernComm {

std::optional<PacketBoundary> PacketFinder::find_packet_boundary(const std::vector<uint8_t>& data) {
    if (data.size() < 4) {  // 最小包大小
        return std::nullopt;
    }
    
    // 查找包头标识
    for (size_t i = 0; i <= data.size() - 2; ++i) {
        // 检查@@包（命令响应）- 0x40 0x40
        if (i + 1 < data.size() && data[i] == 0x40 && data[i + 1] == 0x40) {
            auto boundary = find_command_boundary(data, i);
            if (boundary) return boundary;
        }

        // 检查$$包（IO状态）- 0x24 0x24
        if (i + 1 < data.size() && data[i] == 0x24 && data[i + 1] == 0x24) {
            auto boundary = find_io_boundary(data, i);
            if (boundary) return boundary;
        }

        // 检查%%包（位置数据）- 0x25 0x25
        if (i + 1 < data.size() && data[i] == 0x25 && data[i + 1] == 0x25) {
            auto boundary = find_position_boundary(data, i);
            if (boundary) return boundary;
        }

        // 检查##包（PDO数据）- 0x23 0x23
        if (i + 1 < data.size() && data[i] == 0x23 && data[i + 1] == 0x23) {
            auto boundary = find_pdo_boundary(data, i);
            if (boundary) return boundary;
        }
    }
    
    return std::nullopt;
}

std::optional<PacketBoundary> PacketFinder::find_command_boundary(const std::vector<uint8_t>& data, size_t start_pos) {
    // @@包的基本结构：@@ + 头部(6字节) + 数据 + !!
    if (start_pos + 10 > data.size()) {  // 最小@@包大小
        return std::nullopt;
    }
    
    // 读取数组长度（在@@后的第3-4字节）
    uint16_t array_count = (data[start_pos + 4] << 8) | data[start_pos + 5];
    
    // 计算包的总长度：@@ + 头部(6字节) + 数据(array_count * 2) + !!
    size_t total_length = 2 + 6 + (array_count * 2) + 2;
    
    if (start_pos + total_length > data.size()) {
        return std::nullopt;  // 数据不够
    }
    
    // 检查包尾!!
    if (check_packet_tail(data, start_pos + total_length - 2)) {
        PacketBoundary boundary;
        boundary.start_pos = start_pos;
        boundary.packet_length = total_length;
        boundary.is_complete = true;
        boundary.type = PacketType::Command;
        return boundary;
    }
    
    return std::nullopt;
}

std::optional<PacketBoundary> PacketFinder::find_io_boundary(const std::vector<uint8_t>& data, size_t start_pos) {
    // $$包的基本结构：$$ + 头部 + 数据 + !!
    if (start_pos + 6 > data.size()) {  // 最小$$包大小
        return std::nullopt;
    }

    // 查找包尾!! (0x21 0x21)
    for (size_t i = start_pos + 4; i < data.size() - 1; ++i) {
        if (data[i] == 0x21 && data[i + 1] == 0x21) {
            PacketBoundary boundary;
            boundary.start_pos = start_pos;
            boundary.packet_length = i + 2 - start_pos;
            boundary.is_complete = true;
            boundary.type = PacketType::IO;
            return boundary;
        }
    }

    return std::nullopt;
}

std::optional<PacketBoundary> PacketFinder::find_position_boundary(const std::vector<uint8_t>& data, size_t start_pos) {
    // %%包的基本结构：%% + 头部 + 数据 + !!
    if (start_pos + 6 > data.size()) {  // 最小%%包大小
        return std::nullopt;
    }

    // 查找包尾!! (0x21 0x21)
    for (size_t i = start_pos + 4; i < data.size() - 1; ++i) {
        if (data[i] == 0x21 && data[i + 1] == 0x21) {
            PacketBoundary boundary;
            boundary.start_pos = start_pos;
            boundary.packet_length = i + 2 - start_pos;
            boundary.is_complete = true;
            boundary.type = PacketType::Position;
            return boundary;
        }
    }

    return std::nullopt;
}

std::optional<PacketBoundary> PacketFinder::find_pdo_boundary(const std::vector<uint8_t>& data, size_t start_pos) {
    // ##包的结构：## + 12字节数据 + !!
    size_t total_length = 2 + 12 + 2;  // ## + 数据 + !!
    
    if (start_pos + total_length > data.size()) {
        return std::nullopt;
    }
    
    // 检查包尾!!
    if (check_packet_tail(data, start_pos + total_length - 2)) {
        PacketBoundary boundary;
        boundary.start_pos = start_pos;
        boundary.packet_length = total_length;
        boundary.is_complete = true;
        boundary.type = PacketType::PDO;
        return boundary;
    }
    
    return std::nullopt;
}

bool PacketFinder::check_packet_tail(const std::vector<uint8_t>& data, size_t pos) {
    if (pos + 1 >= data.size()) {
        return false;
    }
    // 包尾是!! (0x21 0x21)，不是ASCII的'!'
    return data[pos] == 0x21 && data[pos + 1] == 0x21;
}

// 实例方法实现
std::optional<PacketBoundary> PacketFinder::find_complete_packet() const {
    if (buffer_.size() < 4) {  // 最小包大小
        return std::nullopt;
    }

    // 遍历缓冲区，查找包头标识
    for (size_t i = 0; i <= buffer_.size() - 2; ++i) {
        // 检查@@包头 (Command)
        if (i + 1 < buffer_.size() && buffer_[i] == 0x40 && buffer_[i + 1] == 0x40) {
            auto boundary = find_command_boundary_in_buffer(i);
            if (boundary && boundary->is_complete) {
                return boundary;
            }
        }
        // 检查$$包头 (IO)
        else if (i + 1 < buffer_.size() && buffer_[i] == 0x24 && buffer_[i + 1] == 0x24) {
            auto boundary = find_io_boundary_in_buffer(i);
            if (boundary && boundary->is_complete) {
                return boundary;
            }
        }
        // 检查%%包头 (Position)
        else if (i + 1 < buffer_.size() && buffer_[i] == 0x25 && buffer_[i + 1] == 0x25) {
            auto boundary = find_position_boundary_in_buffer(i);
            if (boundary && boundary->is_complete) {
                return boundary;
            }
        }
        // 检查##包头 (PDO)
        else if (i + 1 < buffer_.size() && buffer_[i] == 0x23 && buffer_[i + 1] == 0x23) {
            auto boundary = find_pdo_boundary_in_buffer(i);
            if (boundary && boundary->is_complete) {
                return boundary;
            }
        }
    }

    return std::nullopt;
}

std::optional<PacketBoundary> PacketFinder::find_command_boundary_in_buffer(size_t start_pos) const {
    // @@包的结构：@@ + p[2](short flag) + p[3](ArrayCount) + p[4](checksum) + p[5](tick) + 数据 + !!
    if (start_pos + 10 > buffer_.size()) {  // 最小@@包大小：@@ + 头部(6字节) + !! = 10字节
        return std::nullopt;
    }

    // 解析头部参数
    uint16_t flag = (buffer_[start_pos + 3] << 8) | buffer_[start_pos + 2];        // p[2] short flag（小端序）
    uint16_t array_count = (buffer_[start_pos + 5] << 8) | buffer_[start_pos + 4]; // p[3] ArrayCount（小端序）
    uint8_t checksum = buffer_[start_pos + 6];                                     // p[4] checksum
    uint8_t tick = buffer_[start_pos + 7];                                         // p[5] tick

    // 计算包的总长度：@@ + 头部(6字节) + 数据(array_count * 2) + !!
    size_t total_length = 2 + 6 + (array_count * 2) + 2;

    if (start_pos + total_length > buffer_.size()) {
        return std::nullopt;  // 数据不够
    }

    // 检查包尾!!
    if (check_packet_tail_in_buffer(start_pos + total_length - 2)) {
        PacketBoundary boundary;
        boundary.start_pos = start_pos;
        boundary.packet_length = total_length;
        boundary.is_complete = true;
        boundary.type = PacketType::Command;
        return boundary;
    }

    return std::nullopt;
}

std::optional<PacketBoundary> PacketFinder::find_io_boundary_in_buffer(size_t start_pos) const {
    // $$包的结构：$$ + p[2](轴数) + p[3](每轴状态字节数) + p[4](输入组数) + p[5](输出组数) + 轴状态数据 + 输入IO数据 + 输出IO数据 + !!
    if (start_pos + 8 > buffer_.size()) {  // 最小$$包大小：$$ + 4个参数 + !! = 8字节
        return std::nullopt;
    }

    // 解析头部参数
    uint8_t axis_count = buffer_[start_pos + 2];           // p[2] = 轴数
    uint8_t bytes_per_axis = buffer_[start_pos + 3];       // p[3] = 每个轴状态占用字节数（一般是2字节=16位）
    uint8_t input_groups = buffer_[start_pos + 4];         // p[4] = 输入口组数量（每组16位）
    uint8_t output_groups = buffer_[start_pos + 5];        // p[5] = 输出口组数量（每组16位）

    // 计算数据长度
    size_t axis_status_length = axis_count * bytes_per_axis;  // 轴状态数据长度
    size_t input_data_length = input_groups * 2;             // 输入IO数据长度（每组16位，用2字节表示）
    size_t output_data_length = output_groups * 2;           // 输出IO数据长度（每组16位，用2字节表示）

    // 计算总包长度：$$ + 4个参数 + 轴状态数据 + 输入数据 + 输出数据 + !!
    size_t total_length = 2 + 4 + axis_status_length + input_data_length + output_data_length + 2;

    if (start_pos + total_length > buffer_.size()) {
        return std::nullopt;  // 数据不够
    }

    // 检查包尾!!
    if (check_packet_tail_in_buffer(start_pos + total_length - 2)) {
        PacketBoundary boundary;
        boundary.start_pos = start_pos;
        boundary.packet_length = total_length;
        boundary.is_complete = true;
        boundary.type = PacketType::IO;
        return boundary;
    }

    return std::nullopt;
}

std::optional<PacketBoundary> PacketFinder::find_position_boundary_in_buffer(size_t start_pos) const {
    // %%包的结构：%% + p[2](轴数) + p[3](数据类型) + 位置数据 + !!
    if (start_pos + 6 > buffer_.size()) {  // 最小%%包大小：%% + 2个参数 + !! = 6字节
        return std::nullopt;
    }

    // 解析头部参数
    uint8_t axis_count = buffer_[start_pos + 2];        // p[2] = 轴数
    uint8_t data_type = buffer_[start_pos + 3];         // p[3] = 轴位置数据类型

    // 根据数据类型确定每个轴的数据大小
    size_t bytes_per_axis;
    switch (data_type) {
        case 0: bytes_per_axis = 4; break;  // 32位整数
        case 1: bytes_per_axis = 8; break;  // 64位整数
        case 2: bytes_per_axis = 4; break;  // 32位浮点数
        case 3: bytes_per_axis = 8; break;  // 64位浮点数
        default:
            return std::nullopt;  // 未知数据类型
    }

    // 计算位置数据长度
    size_t position_data_length = axis_count * bytes_per_axis;

    // 计算总包长度：%% + 2个参数 + 位置数据 + !!
    size_t total_length = 2 + 2 + position_data_length + 2;

    if (start_pos + total_length > buffer_.size()) {
        return std::nullopt;  // 数据不够
    }

    // 检查包尾!!
    if (check_packet_tail_in_buffer(start_pos + total_length - 2)) {
        PacketBoundary boundary;
        boundary.start_pos = start_pos;
        boundary.packet_length = total_length;
        boundary.is_complete = true;
        boundary.type = PacketType::Position;
        return boundary;
    }

    return std::nullopt;
}

std::optional<PacketBoundary> PacketFinder::find_pdo_boundary_in_buffer(size_t start_pos) const {
    // ##包的结构：## + 12字节数据 + !!
    size_t total_length = 2 + 12 + 2;

    if (start_pos + total_length > buffer_.size()) {
        return std::nullopt;
    }

    // 检查包尾!!
    if (check_packet_tail_in_buffer(start_pos + total_length - 2)) {
        PacketBoundary boundary;
        boundary.start_pos = start_pos;
        boundary.packet_length = total_length;
        boundary.is_complete = true;
        boundary.type = PacketType::PDO;
        return boundary;
    }

    return std::nullopt;
}

bool PacketFinder::check_packet_tail_in_buffer(size_t pos) const {
    if (pos + 1 >= buffer_.size()) {
        return false;
    }
    // 包尾是!! (0x21 0x21)
    return buffer_[pos] == 0x21 && buffer_[pos + 1] == 0x21;
}

} // namespace ModernComm
