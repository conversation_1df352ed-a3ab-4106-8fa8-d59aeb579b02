# ADMotion 通讯协议规范

## 版本信息
- **协议版本**: v1.0
- **文档版本**: v1.0
- **最后更新**: 2025-01-25
- **作者**: ModernComm开发团队

## 1. 协议概述

ADMotion通讯协议是一种基于串口/网络的二进制通讯协议，用于运动控制系统的数据交换。协议支持多种数据包类型，包括位置数据、IO状态、命令控制和PDO数据传输。

### 1.1 协议特点
- **二进制格式**：高效的数据传输
- **包头标识**：不同包类型使用不同的包头标识
- **动态长度**：根据参数动态计算包长度
- **小端序**：多字节数据采用小端序存储
- **错误检测**：包含校验和和长度验证机制

### 1.2 支持的包类型
| 包类型 | 包头标识 | 功能描述 |
|--------|----------|----------|
| 位置包 | `%%` (0x25 0x25) | 传输各轴的位置数据 |
| IO包   | `$$` (0x24 0x24) | 传输轴状态和数字IO数据 |
| 命令包 | `@@` (0x40 0x40) | 传输控制命令和参数 |
| PDO包  | `##` (0x23 0x23) | 传输过程数据对象 |

## 2. 协议通用规则

### 2.1 包结构
所有数据包都遵循以下通用结构：
```
[包头2字节] + [参数N字节] + [数据M字节] + [包尾2字节]
```

### 2.2 包头和包尾
- **包头**：2字节，标识包类型
- **包尾**：固定为 `!!` (0x21 0x21)

### 2.3 字节序
- **多字节整数**：采用小端序（Little Endian）
- **示例**：0x1234 存储为 [0x34, 0x12]

### 2.4 数据类型定义
| 类型 | 大小 | 描述 |
|------|------|------|
| uint8_t | 1字节 | 无符号8位整数 |
| uint16_t | 2字节 | 无符号16位整数（小端序） |
| uint32_t | 4字节 | 无符号32位整数（小端序） |
| int32_t | 4字节 | 有符号32位整数（小端序） |
| int64_t | 8字节 | 有符号64位整数（小端序） |
| float | 4字节 | 32位浮点数（IEEE 754） |
| double | 8字节 | 64位浮点数（IEEE 754） |

## 3. 位置包（%%包）规范

### 3.1 包格式
```
[%%] + [轴数] + [数据类型] + [位置数据] + [!!]
```

### 3.2 参数说明
| 字段 | 偏移 | 类型 | 描述 |
|------|------|------|------|
| 包头 | 0-1 | uint8_t[2] | 固定为 0x25 0x25 (%%) |
| p[2] | 2 | uint8_t | 轴数量 |
| p[3] | 3 | uint8_t | 轴位置数据类型 |
| 位置数据 | 4 | 变长 | 各轴位置数据 |
| 包尾 | N-2,N-1 | uint8_t[2] | 固定为 0x21 0x21 (!!) |

### 3.3 数据类型编码
| 值 | 类型 | 字节数 | 描述 |
|----|------|--------|------|
| 0 | int32_t | 4 | 32位有符号整数 |
| 1 | int64_t | 8 | 64位有符号整数 |
| 2 | float | 4 | 32位浮点数 |
| 3 | double | 8 | 64位浮点数 |

### 3.4 长度计算
```
总长度 = 2(包头) + 2(参数) + 轴数×数据类型字节数 + 2(包尾)
```

### 3.5 示例数据
**4轴32位整数位置包**：
```
25 25 04 00 D0 81 00 00 7D AD 02 00 60 AF 00 00 3E 8E 00 00 21 21
│  │  │  │  │           │           │           │           │  │
│  │  │  │  └─轴0: 33232 │           │           │           │  │
│  │  │  └─数据类型: 0   │           │           │           │  │
│  │  └─轴数: 4          │           │           │           │  │
│  └─包头: %%            │           │           │           │  │
└─包头: %%               │           │           │           │  │
                        └─轴1: 175485│           │           │  │
                                    └─轴2: 44896 │           │  │
                                                └─轴3: 36414 │  │
                                                            └─包尾: !!
```

**解析结果**：
- 轴0位置: 33,232
- 轴1位置: 175,485  
- 轴2位置: 44,896
- 轴3位置: 36,414

## 4. IO包（$$包）规范

### 4.1 包格式
```
[$$] + [轴数] + [每轴状态字节数] + [输入组数] + [输出组数] + [轴状态数据] + [输入IO数据] + [输出IO数据] + [!!]
```

### 4.2 参数说明
| 字段 | 偏移 | 类型 | 描述 |
|------|------|------|------|
| 包头 | 0-1 | uint8_t[2] | 固定为 0x24 0x24 ($$) |
| p[2] | 2 | uint8_t | 轴数量 |
| p[3] | 3 | uint8_t | 每个轴状态占用字节数（一般为2） |
| p[4] | 4 | uint8_t | 输入口组数量（每组16位） |
| p[5] | 5 | uint8_t | 输出口组数量（每组16位） |
| 轴状态数据 | 6 | 变长 | 各轴状态数据 |
| 输入IO数据 | 变长 | 变长 | 数字输入状态 |
| 输出IO数据 | 变长 | 变长 | 数字输出状态 |
| 包尾 | N-2,N-1 | uint8_t[2] | 固定为 0x21 0x21 (!!) |

### 4.3 长度计算
```
轴状态长度 = 轴数 × 每轴状态字节数
输入IO长度 = 输入组数 × 2
输出IO长度 = 输出组数 × 2
总长度 = 2(包头) + 4(参数) + 轴状态长度 + 输入IO长度 + 输出IO长度 + 2(包尾)
```

### 4.4 示例数据
**4轴2字节状态，2组输入，2组输出**：
```
24 24 04 02 02 02 91 0F 91 0F 80 0F 80 0F 00 00 00 00 00 00 00 00 21 21
│  │  │  │  │  │  │           │           │           │           │  │
│  │  │  │  │  │  └─轴0状态───┘           │           │           │  │
│  │  │  │  │  └─输出组数: 2              │           │           │  │
│  │  │  │  └─输入组数: 2                 │           │           │  │
│  │  │  └─每轴状态字节数: 2              │           │           │  │
│  │  └─轴数: 4                          │           │           │  │
│  └─包头: $$                            │           │           │  │
└─包头: $$                               │           │           │  │
                                        └─轴1状态───┘           │  │
                                                    └─轴2状态───┘  │
                                                                └─轴3状态──┘
```

**解析结果**：
- 轴0状态: 0x0F91
- 轴1状态: 0x0F91
- 轴2状态: 0x0F80
- 轴3状态: 0x0F80
- 输入IO: 全部为0
- 输出IO: 全部为0

## 5. 命令包（@@包）规范

### 5.1 包格式
```
[@@] + [flag] + [ArrayCount] + [checksum] + [tick] + [数据数组] + [!!]
```

### 5.2 参数说明
| 字段 | 偏移 | 类型 | 描述 |
|------|------|------|------|
| 包头 | 0-1 | uint8_t[2] | 固定为 0x40 0x40 (@@) |
| p[2] | 2-3 | uint16_t | short flag（保留，暂未使用） |
| p[3] | 4-5 | uint16_t | ArrayCount 数组个数 |
| p[4] | 6 | uint8_t | checksum 校验和（保留） |
| p[5] | 7 | uint8_t | tick 指令索引 |
| 数据数组 | 8 | 变长 | ArrayCount个16位数据 |
| 包尾 | N-2,N-1 | uint8_t[2] | 固定为 0x21 0x21 (!!) |

### 5.3 长度计算
```
数据长度 = ArrayCount × 2
总长度 = 2(包头) + 6(参数) + 数据长度 + 2(包尾)
```

### 5.4 指令索引（tick）机制
- **发送指令**：每个命令包都包含一个唯一的tick值
- **响应匹配**：返回的数据包使用相同的tick值
- **命令追踪**：通过tick实现发送指令与响应数据的匹配
- **取值范围**：0-255（uint8_t）

### 5.5 示例数据
**包含4个数据的命令包**：
```
40 40 34 12 04 00 AB 56 11 11 22 22 33 33 44 44 21 21
│  │  │     │     │  │  │           │           │  │
│  │  │     │     │  │  └─数据0: 0x1111        │  │
│  │  │     │     │  └─tick: 86                │  │
│  │  │     │     └─checksum: 0xAB             │  │
│  │  │     └─ArrayCount: 4                    │  │
│  │  └─flag: 0x1234                           │  │
│  └─包头: @@                                  │  │
└─包头: @@                                     │  │
                                 └─数据1: 0x2222│  │
                                               └─数据2: 0x3333
                                               └─数据3: 0x4444
                                               └─包尾: !!
```

**解析结果**：
- flag: 0x1234（保留字段）
- 数组长度: 4
- 校验和: 0xAB（保留字段）
- 指令索引: 86
- 数据[0]: 4369 (0x1111)
- 数据[1]: 8738 (0x2222)
- 数据[2]: 13107 (0x3333)
- 数据[3]: 17476 (0x4444)

## 6. PDO包（##包）规范

### 6.1 包格式
```
[##] + [参数] + [PDO数据] + [!!]
```

### 6.2 参数说明
| 字段 | 偏移 | 类型 | 描述 |
|------|------|------|------|
| 包头 | 0-1 | uint8_t[2] | 固定为 0x23 0x23 (##) |
| 参数 | 2-N | 变长 | PDO特定参数 |
| PDO数据 | 变长 | 变长 | 过程数据对象 |
| 包尾 | N-2,N-1 | uint8_t[2] | 固定为 0x21 0x21 (!!) |

*注：PDO包的具体格式需要根据实际应用需求进一步定义*

## 7. 实现注意事项

### 7.1 数据解析
1. **字节序处理**：所有多字节数据都使用小端序
2. **动态长度**：根据包头参数动态计算包长度
3. **边界检查**：解析前必须验证数据长度足够
4. **类型转换**：注意有符号和无符号整数的转换

### 7.2 错误处理
1. **包头验证**：检查包头标识是否正确
2. **包尾验证**：检查包尾是否为 `!!`
3. **长度验证**：验证实际数据长度与计算长度是否匹配
4. **参数范围**：检查参数值是否在有效范围内

### 7.3 性能优化
1. **缓冲区管理**：使用环形缓冲区处理连续数据流
2. **包边界检测**：实现高效的包边界识别算法
3. **内存分配**：避免频繁的内存分配和释放
4. **并发处理**：支持多线程环境下的安全访问

## 8. 错误码定义

| 错误码 | 描述 |
|--------|------|
| 0x00 | 成功 |
| 0x01 | 包头错误 |
| 0x02 | 包尾错误 |
| 0x03 | 长度不匹配 |
| 0x04 | 参数超出范围 |
| 0x05 | 校验和错误 |
| 0x06 | 数据类型不支持 |
| 0xFF | 未知错误 |

## 9. 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| v1.0 | 2025-01-25 | 初始版本，定义基本协议格式 |

## 10. 附录

### 10.1 C++结构体定义
```cpp
// 命令包头结构
struct CommandPacketHeader {
    uint8_t header[2];      // @@ 包头
    uint16_t flag;          // 标志字段（保留）
    uint16_t array_count;   // 数组个数
    uint8_t checksum;       // 校验和（保留）
    uint8_t tick;           // 指令索引
} __attribute__((packed));

// 解析后的命令包数据
struct ParsedCommandPacket {
    uint16_t flag;              // 标志字段
    uint16_t array_count;       // 数组个数
    uint8_t checksum;           // 校验和
    uint8_t tick;               // 指令索引
    std::vector<uint16_t> data; // 数据数组
};
```

### 10.2 示例代码
```cpp
// 解析位置包示例
auto parsed = processor.parse_position_packet(packet_data);
if (parsed) {
    // 解析成功，处理位置数据
    process_position_data(parsed->data);
}

// 解析命令包示例
auto cmd_parsed = processor.parse_command_packet(packet_data);
if (cmd_parsed) {
    // 解析成功，处理命令数据
    process_command_data(cmd_parsed->data);
}
```

---

**文档结束**

*本文档为ADMotion通讯协议的技术规范，如有疑问或建议，请联系开发团队。*
