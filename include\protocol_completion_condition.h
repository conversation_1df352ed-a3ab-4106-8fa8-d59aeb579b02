/**
 * @file protocol_completion_condition.h
 * @brief ASIO自定义完成条件 - 用于ADMotion协议的分包处理
 * @details 提供给asio::async_read使用的完成条件，自动识别完整的ADMotion数据包
 */

#ifndef PROTOCOL_COMPLETION_CONDITION_H
#define PROTOCOL_COMPLETION_CONDITION_H

#include <asio.hpp>
#include "protocol_processor.h"
#include <optional>
#include <cstdint>

namespace ModernComm {

/**
 * @brief ADMotion协议包查找器
 * 作为asio::async_read的完成条件，自动识别完整的数据包
 */
class PacketFinder {
private:
    mutable std::vector<uint8_t> buffer_;  // 内部缓冲区，用于累积数据

public:
    /**
     * @brief 完成条件函数
     * @details 这个函数会被asio::async_read在每次读到新数据后调用
     * @param ec 错误代码
     * @param bytes_transferred 本次传输的字节数
     * @return 如果找到完整包，返回包的总长度；否则返回0继续读取
     */
    std::size_t operator()(const asio::error_code& ec, std::size_t /*bytes_transferred*/) {
        if (ec) {
            return 0; // 如果有错误，停止读取
        }

        // 注意：这个函数会在每次读取操作后被调用
        // bytes_transferred 表示本次读取的字节数
        // 实际的缓冲区数据需要从外部传入或通过其他方式获取

        // 这里我们返回0表示继续读取，直到外部逻辑决定停止
        // 实际的包检测逻辑将在 AsioTransport 中处理
        return 0;
    }

    /**
     * @brief 设置内部缓冲区数据（用于包检测）
     * @param data 缓冲区数据
     */
    void set_buffer_data(const std::vector<uint8_t>& data) {
        buffer_ = data;
    }

    /**
     * @brief 检查当前缓冲区是否包含完整的包
     * @return 如果找到完整包，返回包的长度；否则返回0
     */
    std::size_t check_complete_packet() {
        auto boundary = find_complete_packet();
        if (boundary) {
            return boundary->packet_length;
        }
        return 0;
    }

    /**
     * @brief 获取并清空内部缓冲区的数据
     * @return 缓冲区中的完整包数据
     */
    std::vector<uint8_t> extract_packet() const {
        auto result = buffer_;
        buffer_.clear();
        return result;
    }

public:
    /**
     * @brief 查找数据流中的包边界
     * @param data 数据缓冲区
     * @return 包边界信息，如果没有找到完整包则返回nullopt
     */
    static std::optional<PacketBoundary> find_packet_boundary(const std::vector<uint8_t>& data);
    
    /**
     * @brief 查找命令包边界（@@包）
     */
    static std::optional<PacketBoundary> find_command_boundary(const std::vector<uint8_t>& data, size_t start_pos);
    
    /**
     * @brief 查找IO状态包边界（$包）
     */
    static std::optional<PacketBoundary> find_io_boundary(const std::vector<uint8_t>& data, size_t start_pos);
    
    /**
     * @brief 查找位置数据包边界（%%包）
     */
    static std::optional<PacketBoundary> find_position_boundary(const std::vector<uint8_t>& data, size_t start_pos);
    
    /**
     * @brief 查找PDO数据包边界（##包）
     */
    static std::optional<PacketBoundary> find_pdo_boundary(const std::vector<uint8_t>& data, size_t start_pos);
    
    /**
     * @brief 检查包尾标识（!!）
     */
    static bool check_packet_tail(const std::vector<uint8_t>& data, size_t pos);

private:
    /**
     * @brief 在缓冲区中查找完整的ADMotion包
     * @return 如果找到完整包，返回包边界信息；否则返回nullopt
     */
    std::optional<PacketBoundary> find_complete_packet() const;

    /**
     * @brief 在缓冲区中查找@@包的边界
     */
    std::optional<PacketBoundary> find_command_boundary_in_buffer(size_t start_pos) const;

    /**
     * @brief 在缓冲区中查找$$包的边界
     */
    std::optional<PacketBoundary> find_io_boundary_in_buffer(size_t start_pos) const;

    /**
     * @brief 在缓冲区中查找%%包的边界
     */
    std::optional<PacketBoundary> find_position_boundary_in_buffer(size_t start_pos) const;

    /**
     * @brief 在缓冲区中查找##包的边界
     */
    std::optional<PacketBoundary> find_pdo_boundary_in_buffer(size_t start_pos) const;

    /**
     * @brief 在缓冲区中检查包尾标识
     */
    bool check_packet_tail_in_buffer(size_t pos) const;
};

} // namespace ModernComm

#endif // PROTOCOL_COMPLETION_CONDITION_H
