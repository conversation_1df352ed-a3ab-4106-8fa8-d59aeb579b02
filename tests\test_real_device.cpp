/**
 * @file test_real_device.cpp
 * @brief 真实设备连接测试 - 连接***********接收位置和IO信息
 */

#include "../include/communication_manager.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <iomanip>

using namespace ModernComm;

// 辅助函数：打印十六进制数据
void print_hex_data(const std::vector<uint8_t>& data, const std::string& prefix = "") {
    std::cout << prefix;
    for (size_t i = 0; i < data.size(); ++i) {
        printf("%02X ", data[i]);
        if ((i + 1) % 16 == 0) {
            std::cout << std::endl << prefix;
        }
    }
    if (data.size() % 16 != 0) {
        std::cout << std::endl;
    }
}

// 解析位置数据包（%%包）
void parse_position_packet(const std::vector<uint8_t>& data) {
    std::cout << "📍 [位置包解析]" << std::endl;
    std::cout << "原始数据大小: " << data.size() << " 字节" << std::endl;
    print_hex_data(data, "  ");
    
    if (data.size() >= 4) {
        // 假设前两个字节是轴数量和位置类型
        uint8_t axis_count = data[0];
        uint8_t position_type = data[1];
        
        std::cout << "轴数量: " << static_cast<int>(axis_count) << std::endl;
        std::cout << "位置类型: " << static_cast<int>(position_type) << std::endl;
        
        // 根据位置类型解析位置数据
        size_t pos_size = 4; // 默认32位
        if (position_type == 1) pos_size = 8; // 64位
        
        std::cout << "位置数据:" << std::endl;
        for (int i = 0; i < axis_count && (2 + i * pos_size + pos_size) <= data.size(); ++i) {
            if (pos_size == 4) {
                // 32位位置数据
                int32_t position = *reinterpret_cast<const int32_t*>(&data[2 + i * pos_size]);
                std::cout << "  轴" << i << ": " << position << std::endl;
            } else if (pos_size == 8) {
                // 64位位置数据
                int64_t position = *reinterpret_cast<const int64_t*>(&data[2 + i * pos_size]);
                std::cout << "  轴" << i << ": " << position << std::endl;
            }
        }
    }
}

// 解析IO数据包（$包）
void parse_io_packet(const std::vector<uint8_t>& data) {
    std::cout << "🔌 [IO包解析]" << std::endl;
    std::cout << "原始数据大小: " << data.size() << " 字节" << std::endl;
    print_hex_data(data, "  ");
    
    if (data.size() >= 4) {
        uint8_t axis_count = data[0];
        uint8_t input_groups = data[1];
        uint8_t output_groups = data[2];
        
        std::cout << "轴数量: " << static_cast<int>(axis_count) << std::endl;
        std::cout << "输入组数: " << static_cast<int>(input_groups) << std::endl;
        std::cout << "输出组数: " << static_cast<int>(output_groups) << std::endl;
        
        // 简单解析IO状态（具体格式需要根据实际协议调整）
        size_t offset = 4;
        std::cout << "IO状态数据:" << std::endl;
        for (size_t i = offset; i < data.size() && i < offset + 16; ++i) {
            std::cout << "  字节" << (i-offset) << ": 0x" << std::hex << std::setw(2) << std::setfill('0') 
                      << static_cast<int>(data[i]) << std::dec << std::endl;
        }
    }
}

// 解析PDO数据包（##包）
void parse_pdo_packet(const std::vector<uint8_t>& data) {
    std::cout << "📊 [PDO包解析]" << std::endl;
    std::cout << "PDO数据大小: " << data.size() << " 字节 (应该是12字节)" << std::endl;
    print_hex_data(data, "  ");
    
    if (data.size() == 12) {
        std::cout << "PDO数据解析:" << std::endl;
        for (int i = 0; i < 3; ++i) {
            // 假设每4字节是一个32位数据
            int32_t value = *reinterpret_cast<const int32_t*>(&data[i * 4]);
            std::cout << "  PDO" << i << ": " << value << std::endl;
        }
    }
}

// 解析命令响应包（@@包）
void parse_command_packet(const std::vector<uint8_t>& data) {
    std::cout << "💻 [命令响应包解析]" << std::endl;
    std::cout << "响应数据大小: " << data.size() << " 字节" << std::endl;
    print_hex_data(data, "  ");
    
    if (data.size() >= 8) {
        // 解析命令响应头部
        uint16_t flag = *reinterpret_cast<const uint16_t*>(&data[0]);
        uint16_t array_count = *reinterpret_cast<const uint16_t*>(&data[2]);
        uint16_t checksum = *reinterpret_cast<const uint16_t*>(&data[4]);
        uint16_t tick = *reinterpret_cast<const uint16_t*>(&data[6]);
        
        std::cout << "标志: 0x" << std::hex << flag << std::dec << std::endl;
        std::cout << "数组长度: " << array_count << std::endl;
        std::cout << "校验和: 0x" << std::hex << checksum << std::dec << std::endl;
        std::cout << "时钟: 0x" << std::hex << tick << std::dec << std::endl;
        
        // 解析命令数据
        std::cout << "命令数据:" << std::endl;
        for (int i = 0; i < array_count && (8 + i * 2 + 2) <= data.size(); ++i) {
            int16_t cmd_value = *reinterpret_cast<const int16_t*>(&data[8 + i * 2]);
            std::cout << "  命令" << i << ": " << cmd_value << std::endl;
        }
    }
}

void test_real_device_connection() {
    std::cout << "\n=== 真实设备连接测试 ===" << std::endl;
    std::cout << "连接目标: ***********:6666" << std::endl;
    
    // 创建通讯管理器
    auto comm_manager = CommunicationFactory::create_asio_manager();
    
    // 设置包接收回调 - 根据包类型进行不同的解析
    comm_manager->set_packet_callback([](ParsedPacket&& packet) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        std::cout << "\n⏰ [" << std::put_time(std::localtime(&time_t), "%H:%M:%S") << "] ";
        
        switch (packet.type) {
            case PacketType::Position:
                std::cout << "收到位置数据包" << std::endl;
                parse_position_packet(packet.data);
                break;
                
            case PacketType::IO:
                std::cout << "收到IO状态包" << std::endl;
                parse_io_packet(packet.data);
                break;
                
            case PacketType::PDO:
                std::cout << "收到PDO数据包" << std::endl;
                parse_pdo_packet(packet.data);
                break;
                
            case PacketType::Command:
                std::cout << "收到命令响应包" << std::endl;
                parse_command_packet(packet.data);
                break;
                
            default:
                std::cout << "收到未知类型包: " << static_cast<int>(packet.type) << std::endl;
                print_hex_data(packet.data, "  ");
                break;
        }
        
        std::cout << "----------------------------------------" << std::endl;
    });
    
    // 设置连接状态回调
    comm_manager->set_connection_callback([](bool connected) {
        if (connected) {
            std::cout << "✅ 设备连接成功！开始接收数据..." << std::endl;
        } else {
            std::cout << "❌ 设备连接断开" << std::endl;
        }
    });
    
    // 设置错误回调
    comm_manager->set_error_callback([](const std::string& error_msg) {
        std::cout << "⚠️  通讯错误: " << error_msg << std::endl;
    });
    
    // 尝试连接到真实设备
    std::cout << "正在连接到 ***********:6666..." << std::endl;
    auto connect_future = comm_manager->connect_async("***********", 6666);
    
    // 等待连接结果
    try {
        // 等待最多5秒
        auto status = connect_future.wait_for(std::chrono::seconds(5));
        
        if (status == std::future_status::ready) {
            bool connected = connect_future.get();
            
            if (connected) {
                std::cout << "🎉 连接成功！" << std::endl;
                std::cout << "等待设备数据..." << std::endl;
                std::cout << "========================================" << std::endl;
                
                // 保持连接，接收数据30秒
                for (int i = 0; i < 30; ++i) {
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                    
                    // 每10秒显示一次统计信息
                    if ((i + 1) % 10 == 0) {
                        auto stats = comm_manager->get_statistics();
                        std::cout << "\n📊 [统计信息 - " << (i + 1) << "秒]" << std::endl;
                        std::cout << "接收字节数: " << stats.bytes_received << std::endl;
                        std::cout << "接收包数: " << stats.packets_received << std::endl;
                        std::cout << "发送字节数: " << stats.bytes_sent << std::endl;
                        std::cout << "发送包数: " << stats.packets_sent << std::endl;
                        std::cout << "========================================" << std::endl;
                    }
                    
                    // 检查连接状态
                    if (!comm_manager->is_connected()) {
                        std::cout << "连接已断开，退出监听" << std::endl;
                        break;
                    }
                }
                
            } else {
                std::cout << "❌ 连接失败" << std::endl;
            }
        } else {
            std::cout << "⏱️  连接超时" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "连接异常: " << e.what() << std::endl;
    }
    
    // 断开连接
    std::cout << "\n正在断开连接..." << std::endl;
    comm_manager->disconnect();
    
    // 最终统计
    auto final_stats = comm_manager->get_statistics();
    std::cout << "\n📈 [最终统计]" << std::endl;
    std::cout << "总接收字节数: " << final_stats.bytes_received << std::endl;
    std::cout << "总接收包数: " << final_stats.packets_received << std::endl;
    std::cout << "总发送字节数: " << final_stats.bytes_sent << std::endl;
    std::cout << "总发送包数: " << final_stats.packets_sent << std::endl;
    
    std::cout << "真实设备测试完成" << std::endl;
}

int main() {
    std::cout << "ModernCommLib 真实设备连接测试" << std::endl;
    std::cout << "========================================" << std::endl;
    
    try {
        test_real_device_connection();
        
    } catch (const std::exception& e) {
        std::cout << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}