/**
 * @file simple_transport.h
 * @brief 最简单的网络传输层接口
 * @details 从最基础的概念开始，逐步构建完整的传输层
 */

#ifndef SIMPLE_TRANSPORT_H
#define SIMPLE_TRANSPORT_H

#include <string>
#include <future>
#include <functional>
#include <vector>
#include <memory>

namespace ModernComm {

/**
 * @brief 最简单的传输层接口             纯虚函数  ,接口函数,只是实现接口 
 * 先定义最基本的功能，后续逐步扩展
 */
class SimpleTransport {
public:
    // 析构函数
    virtual ~SimpleTransport() = default;
    
    // 最基本的三个功能
    
    /**
     * @brief 异步连接到服务器
     * @param host 主机地址
     * @param port 端口号
     * @return future对象，可以获取连接结果
     */
    virtual std::future<bool> connect_async(const std::string& host, int port) = 0;
    
    /**
     * @brief 异步发送数据
     * @param data 要发送的数据
     * @return future对象，可以获取发送结果
     */
    virtual std::future<bool> send_async(const std::vector<uint8_t>& data) = 0;
    
    /**
     * @brief 设置数据接收回调
     * @param callback 收到数据时调用的函数
     */
    virtual void set_receive_callback(std::function<void(const std::vector<uint8_t>&)> callback) = 0;
    
    /**
     * @brief 检查连接状态
     * @return true表示已连接
     */
    virtual bool is_connected() const = 0;
    
    /**
     * @brief 断开连接
     */
    virtual void disconnect() = 0;
};

} // namespace ModernComm

#endif // SIMPLE_TRANSPORT_H