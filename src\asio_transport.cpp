// =====================================================
// 文件: asio_transport.cpp (已修正)
// =====================================================
#include "../include/asio_transport.h" // 修正包含路径
#include "../include/protocol_completion_condition.h"
#include <iostream>

namespace ModernComm {

// **修正2：在构造函数初始化列表中初始化 work_guard_**
AsioTransport::AsioTransport()
    : work_guard_(asio::make_work_guard(io_context_)), // 确保 work_guard_ 与 io_context_ 关联
      socket_(std::make_unique<asio::ip::tcp::socket>(io_context_)),
      receive_buffer_(1024),
      packet_finder_(std::make_unique<PacketFinder>())
{
    std::cout << "AsioTransport 构造函数被调用" << std::endl;
    start_io_thread();
}

AsioTransport::~AsioTransport() {
    std::cout << "AsioTransport 析构函数被调用" << std::endl;
    disconnect();
    stop_io_thread();
}

void AsioTransport::start_io_thread() {
    io_thread_ = std::make_unique<std::thread>([this]() {
        std::cout << "IO线程启动" << std::endl;
        try {
            // 有了 work_guard_，run() 会一直阻塞直到被停止，不再需要while循环
            io_context_.run();
        } catch (const std::exception& e) {
            std::cout << "IO线程异常: " << e.what() << std::endl;
        }
        std::cout << "IO线程结束" << std::endl;
    });
}

// **修正4：在停止线程前，重置 work_guard_**
void AsioTransport::stop_io_thread() {
    // 1. 解除守护，允许 io_context.run() 在任务完成后退出
    work_guard_.reset();

    // 2. 停止 io_context，这会取消所有挂起的异步操作
    if (!io_context_.stopped()) {
        io_context_.stop();
    }
    
    // 3. 等待IO线程完全结束
    if (io_thread_ && io_thread_->joinable()) {
        io_thread_->join();
    }
}

bool AsioTransport::is_connected() const {
    return connected_.load();
}

void AsioTransport::disconnect() {
    std::cout << "断开连接" << std::endl;
    
    // 在IO线程中执行关闭操作，避免竞态条件
    if (connected_.exchange(false)) { // exchange可以原子地设置新值并返回旧值
        asio::post(io_context_, [this]() {
            if (socket_ && socket_->is_open()) {
                std::error_code ec;
                socket_->shutdown(asio::ip::tcp::socket::shutdown_both, ec);
                socket_->close(ec);
                std::cout << "socket已关闭" << std::endl;
            }
        });
    }
}

// connect_async, send_async, 等其他函数保持不变...
// (为简洁起见，此处省略，它们在修正后的模型下可以正常工作)

std::future<bool> AsioTransport::connect_async(const std::string& host, int port) {
    std::cout << "connect_async 被调用: " << host << ":" << port << std::endl;
    
    auto promise = std::make_shared<std::promise<bool>>();
    auto future = promise->get_future();
    
    asio::post(io_context_, [this, host, port, promise]() {
        try {
            asio::ip::tcp::resolver resolver(io_context_);
            auto endpoints = resolver.resolve(host, std::to_string(port));
            
            asio::async_connect(*socket_, endpoints,
                [this, promise](const std::error_code& ec, const asio::ip::tcp::endpoint& endpoint) {
                    if (!ec) {
                        connected_.store(true);
                        std::cout << "连接成功! 连接到: " << endpoint.address().to_string()
                                  << ":" << endpoint.port() << std::endl;
                        start_receive_with_packet_finder();
                        promise->set_value(true);
                    } else {
                        std::cout << "连接失败: " << ec.message() << std::endl;
                        promise->set_value(false);
                    }
                });
        } catch (const std::exception& e) {
            std::cout << "连接异常: " << e.what() << std::endl;
            promise->set_value(false);
        }
    });
    
    return future;
}

std::future<bool> AsioTransport::send_async(const std::vector<uint8_t>& data) {
    auto promise = std::make_shared<std::promise<bool>>();
    auto future = promise->get_future();
    
    if (!connected_.load()) {
        promise->set_value(false);
        return future;
    }
    
    auto buffer = std::make_shared<std::vector<uint8_t>>(data);
    
    asio::post(io_context_, [this, buffer, promise]() {
        asio::async_write(*socket_, asio::buffer(*buffer),
            [promise, buffer](const std::error_code& ec, size_t bytes_transferred) {
                if (!ec) {
                    promise->set_value(true);
                } else {
                    promise->set_value(false);
                }
            });
    });
    
    return future;
}

void AsioTransport::set_receive_callback(std::function<void(const std::vector<uint8_t>&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    receive_callback_ = std::move(callback);
}

void AsioTransport::set_packet_finder(std::unique_ptr<PacketFinder> finder) {
    packet_finder_ = std::move(finder);
    std::cout << "PacketFinder 已设置到 AsioTransport" << std::endl;
}



void AsioTransport::start_receive_with_packet_finder() {
    if (!connected_.load() || !socket_ || !socket_->is_open()) {
        return;
    }

    // 使用动态缓冲区进行异步读取，直到找到完整的包
    asio::async_read(*socket_, dynamic_buffer_, asio::transfer_at_least(1),
        [this](const std::error_code& ec, size_t bytes_received) {
            handle_packet_receive(ec, bytes_received);
        }
    );
}

void AsioTransport::handle_packet_receive(const std::error_code& ec, size_t bytes_received) {
    if (!ec) {
        std::cout << "接收到 " << bytes_received << " 字节数据，缓冲区总大小: "
                  << dynamic_buffer_.size() << std::endl;

        // 处理接收到的数据，查找完整的包
        process_received_data();

        // 继续接收更多数据
        if (connected_.load()) {
            start_receive_with_packet_finder();
        }
    } else {
        if (ec != asio::error::operation_aborted) {
            std::cout << "接收错误: " << ec.message() << std::endl;
            disconnect();
        }
    }
}

void AsioTransport::process_received_data() {
    // 将动态缓冲区的数据转换为vector
    auto buffer_data = dynamic_buffer_.data();
    std::vector<uint8_t> data_vector;
    data_vector.reserve(dynamic_buffer_.size());

    for (auto it = asio::buffers_begin(buffer_data);
         it != asio::buffers_end(buffer_data); ++it) {
        data_vector.push_back(*it);
    }

    // 使用PacketFinder检查是否有完整的包
    if (packet_finder_) {
        packet_finder_->set_buffer_data(data_vector);
        size_t packet_length = packet_finder_->check_complete_packet();

        if (packet_length > 0 && packet_length <= data_vector.size()) {
            std::cout << "找到完整包，长度: " << packet_length << std::endl;

            // 提取完整的包数据
            std::vector<uint8_t> complete_packet(data_vector.begin(),
                                                data_vector.begin() + packet_length);

            // 调用回调函数传递完整的包
            {
                std::lock_guard<std::mutex> lock(callback_mutex_);
                if (receive_callback_) {
                    receive_callback_(complete_packet);
                }
            }

            // 从动态缓冲区中移除已处理的数据
            dynamic_buffer_.consume(packet_length);

            // 如果缓冲区中还有数据，递归处理
            if (dynamic_buffer_.size() > 0) {
                process_received_data();
            }
        }
        // 如果没有找到完整包，保持数据在缓冲区中，等待更多数据
    }
}

} // namespace ModernComm