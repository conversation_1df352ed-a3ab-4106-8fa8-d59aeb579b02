/**
 * @file simple_connection_test.cpp
 * @brief 简单的连接测试，直接使用AsioTransport
 */

#include "../include/asio_transport.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace ModernComm;

int main() {
    std::cout << "简单连接测试" << std::endl;
    std::cout << "===================" << std::endl;
    
    try {
        // 创建传输层
        AsioTransport transport;
        
        // 设置接收回调
        transport.set_receive_callback([](const std::vector<uint8_t>& data) {
            std::cout << "收到数据: " << data.size() << " 字节" << std::endl;
            // 打印前几个字节
            for (size_t i = 0; i < std::min(data.size(), size_t(16)); ++i) {
                printf("%02X ", data[i]);
            }
            std::cout << std::endl;
        });
        
        // 尝试连接
        std::cout << "尝试连接到 192.168.2.2:6666..." << std::endl;
        auto connect_future = transport.connect_async("192.168.2.2", 6666);
        
        // 等待连接结果
        std::cout << "等待连接结果（5秒超时）..." << std::endl;
        auto status = connect_future.wait_for(std::chrono::seconds(5));
        
        if (status == std::future_status::ready) {
            bool connected = connect_future.get();
            if (connected) {
                std::cout << "✅ 连接成功！" << std::endl;
                
                // 保持连接几秒钟，看看是否有数据
                std::cout << "保持连接5秒，监听数据..." << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(5));
                
                std::cout << "断开连接..." << std::endl;
                transport.disconnect();
                
            } else {
                std::cout << "❌ 连接失败" << std::endl;
            }
        } else {
            std::cout << "⏱️  连接超时" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "测试完成" << std::endl;
    return 0;
}