# ModernCommLib - 现代C++通讯库学习项目

## 项目目标
从零开始学习现代C++编程技巧，通过构建一个完整的网络通讯库来掌握：

1. **C++17基础特性**
   - 智能指针 (unique_ptr, shared_ptr)
   - 异步编程 (future/promise)
   - 现代容器和算法
   - RAII资源管理

2. **ASIO网络编程**
   - 异步I/O模型
   - TCP连接管理
   - 数据收发处理

3. **软件架构设计**
   - 分层架构
   - 接口设计
   - 错误处理

## 学习路径
- 第1阶段：C++17基础工具类
- 第2阶段：简单的网络传输层
- 第3阶段：协议解析层
- 第4阶段：应用管理层
- 第5阶段：与ADMotion项目集成

## 编译要求
- C++17标准
- ASIO 1.30.2
- MinGW编译器