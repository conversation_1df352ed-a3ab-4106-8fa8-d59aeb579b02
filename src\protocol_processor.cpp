/**
 * @file protocol_processor.cpp
 * @brief ADMotion协议处理器实现
 */

#include "../include/protocol_processor.h"
#include <iostream>
#include <algorithm>
#include <iomanip>

namespace ModernComm {

ADMotionProtocolProcessor::ADMotionProtocolProcessor() {
    std::cout << "ADMotion协议处理器已创建" << std::endl;
}

void ADMotionProtocolProcessor::process_complete_packet(const std::vector<uint8_t>& data) {
    std::cout << "协议处理器收到完整包 " << data.size() << " 字节数据" << std::endl;

    // 打印原始数据的十六进制内容进行调试
    std::cout << "原始数据内容: ";
    for (size_t i = 0; i < std::min(data.size(), size_t(32)); ++i) {
        printf("%02X ", data[i]);
        if ((i + 1) % 8 == 0) std::cout << " ";
    }
    if (data.size() > 32) std::cout << "...";
    std::cout << std::endl;

    // 打印ASCII可见字符
    std::cout << "ASCII内容: ";
    for (size_t i = 0; i < std::min(data.size(), size_t(32)); ++i) {
        char c = static_cast<char>(data[i]);
        if (c >= 32 && c <= 126) {
            std::cout << c;
        } else {
            std::cout << ".";
        }
    }
    std::cout << std::endl;

    // 现在收到的data已经保证是一个完整的包（由AsioTransport的PacketFinder保证）
    // 我们只需要解析包的内容即可，不需要再查找边界

    // 直接解析包内容
    auto packet = parse_complete_packet_data(data);
    if (packet) {
        // 成功解析包
        total_packets_processed_++;
        std::cout << "成功解析包，类型: " << static_cast<int>(packet->type) << std::endl;

        if (packet_handler_) {
            packet_handler_(std::move(*packet));
        }
    } else {
        // 解析失败
        parse_errors_++;
        std::cout << "包解析失败" << std::endl;
        if (error_handler_) {
            error_handler_("完整包解析失败");
        }
    }
}

std::optional<PacketBoundary> ADMotionProtocolProcessor::find_packet_boundary(const std::vector<uint8_t>& data) {
    if (data.size() < 4) {  // 最小包大小
        std::cout << "数据太短，无法构成有效包 (< 4字节)" << std::endl;
        return std::nullopt;
    }

    std::cout << "查找包边界，数据长度: " << data.size() << std::endl;

    // 查找包头标识
    for (size_t i = 0; i <= data.size() - 2; ++i) {
        // 检查@@包（命令响应）- 0x40 0x40
        if (i + 1 < data.size() && data[i] == 0x40 && data[i + 1] == 0x40) {
            std::cout << "在位置 " << i << " 找到@@包头 (0x40 0x40)" << std::endl;
            auto boundary = find_command_boundary(data, i);
            if (boundary) return boundary;
        }

        // 检查$$包（IO状态）- 0x24 0x24
        if (i + 1 < data.size() && data[i] == 0x24 && data[i + 1] == 0x24) {
            std::cout << "在位置 " << i << " 找到$$包头 (0x24 0x24)" << std::endl;
            auto boundary = find_io_boundary(data, i);
            if (boundary) return boundary;
        }

        // 检查%%包（位置数据）- 0x25 0x25
        if (i + 1 < data.size() && data[i] == 0x25 && data[i + 1] == 0x25) {
            std::cout << "在位置 " << i << " 找到%%包头 (0x25 0x25)" << std::endl;
            auto boundary = find_position_boundary(data, i);
            if (boundary) return boundary;
        }

        // 检查##包（PDO数据）- 0x23 0x23
        if (i + 1 < data.size() && data[i] == 0x23 && data[i + 1] == 0x23) {
            std::cout << "在位置 " << i << " 找到##包头 (0x23 0x23)" << std::endl;
            auto boundary = find_pdo_boundary(data, i);
            if (boundary) return boundary;
        }
    }

    std::cout << "未找到任何已知的包头标识 (@@, $, %%, ##)" << std::endl;
    return std::nullopt;
}

std::optional<PacketBoundary> ADMotionProtocolProcessor::find_command_boundary(const std::vector<uint8_t>& data, size_t start_pos) {
    // @@包的结构：@@ + p[2](short flag) + p[3](ArrayCount) + p[4](checksum) + p[5](tick) + 数据 + !!
    if (start_pos + 10 > data.size()) {  // 最小@@包大小：@@ + 头部(6字节) + !! = 10字节
        std::cout << "@@包数据不足，需要至少10字节" << std::endl;
        return std::nullopt;
    }

    // 解析头部参数
    uint16_t flag = (data[start_pos + 3] << 8) | data[start_pos + 2];        // p[2] short flag（小端序）
    uint16_t array_count = (data[start_pos + 5] << 8) | data[start_pos + 4]; // p[3] ArrayCount（小端序）
    uint8_t checksum = data[start_pos + 6];                                  // p[4] checksum
    uint8_t tick = data[start_pos + 7];                                      // p[5] tick

    std::cout << "@@包参数: flag=0x" << std::hex << flag << std::dec
              << ", 数组长度=" << array_count
              << ", 校验和=0x" << std::hex << (int)checksum << std::dec
              << ", 指令索引=" << (int)tick << std::endl;

    // 计算包的总长度：@@ + 头部(6字节) + 数据(array_count * 2) + !!
    size_t total_length = 2 + 6 + (array_count * 2) + 2;

    std::cout << "@@包计算长度: " << total_length << " 字节" << std::endl;

    if (start_pos + total_length > data.size()) {
        std::cout << "@@包数据不足，需要 " << total_length << " 字节，实际只有 "
                  << (data.size() - start_pos) << " 字节" << std::endl;
        return std::nullopt;  // 数据不够
    }

    // 检查包尾!!
    if (check_packet_tail(data, start_pos + total_length - 2)) {
        std::cout << "在位置 " << (start_pos + total_length - 2) << " 找到@@包尾 (0x21 0x21)" << std::endl;
        PacketBoundary boundary;
        boundary.start_pos = start_pos;
        boundary.packet_length = total_length;
        boundary.is_complete = true;
        boundary.type = PacketType::Command;
        return boundary;
    }

    std::cout << "@@包尾标识不匹配，期望!! (0x21 0x21)，实际: "
              << std::hex << (int)data[start_pos + total_length - 2] << " "
              << (int)data[start_pos + total_length - 1] << std::dec << std::endl;
    return std::nullopt;
}

std::optional<PacketBoundary> ADMotionProtocolProcessor::find_io_boundary(const std::vector<uint8_t>& data, size_t start_pos) {
    // $$包的结构：$$ + p[2](轴数) + p[3](每轴状态字节数) + p[4](输入组数) + p[5](输出组数) + 轴状态数据 + 输入IO数据 + 输出IO数据 + !!
    if (start_pos + 8 > data.size()) {  // 最小$$包大小：$$ + 4个参数 + !! = 8字节
        std::cout << "$$包数据不足，需要至少8字节" << std::endl;
        return std::nullopt;
    }

    // 解析头部参数
    uint8_t axis_count = data[start_pos + 2];           // p[2] = 轴数
    uint8_t bytes_per_axis = data[start_pos + 3];       // p[3] = 每个轴状态占用字节数（一般是2字节=16位）
    uint8_t input_groups = data[start_pos + 4];         // p[4] = 输入口组数量（每组16位）
    uint8_t output_groups = data[start_pos + 5];        // p[5] = 输出口组数量（每组16位）

    std::cout << "$$包参数: 轴数=" << (int)axis_count
              << ", 每轴状态字节数=" << (int)bytes_per_axis
              << ", 输入组数=" << (int)input_groups
              << ", 输出组数=" << (int)output_groups << std::endl;

    // 计算数据长度
    size_t axis_status_length = axis_count * bytes_per_axis;  // 轴状态数据长度
    size_t input_data_length = input_groups * 2;             // 输入IO数据长度（每组16位，用2字节表示）
    size_t output_data_length = output_groups * 2;           // 输出IO数据长度（每组16位，用2字节表示）

    // 计算总包长度：$$ + 4个参数 + 轴状态数据 + 输入数据 + 输出数据 + !!
    size_t total_length = 2 + 4 + axis_status_length + input_data_length + output_data_length + 2;

    std::cout << "$$包数据长度计算: 轴状态=" << axis_status_length
              << "字节, 输入IO=" << input_data_length
              << "字节, 输出IO=" << output_data_length
              << "字节, 总长度=" << total_length << "字节" << std::endl;

    if (start_pos + total_length > data.size()) {
        std::cout << "$$包数据不足，需要 " << total_length << " 字节，实际只有 "
                  << (data.size() - start_pos) << " 字节" << std::endl;
        return std::nullopt;  // 数据不够
    }

    // 检查包尾!!
    if (data[start_pos + total_length - 2] == 0x21 &&
        data[start_pos + total_length - 1] == 0x21) {
        std::cout << "在位置 " << (start_pos + total_length - 2) << " 找到$$包尾 (0x21 0x21)" << std::endl;
        PacketBoundary boundary;
        boundary.start_pos = start_pos;
        boundary.packet_length = total_length;
        boundary.is_complete = true;
        boundary.type = PacketType::IO;
        return boundary;
    }

    std::cout << "$$包尾标识不匹配，期望!! (0x21 0x21)，实际: "
              << std::hex << (int)data[start_pos + total_length - 2] << " "
              << (int)data[start_pos + total_length - 1] << std::dec << std::endl;
    return std::nullopt;
}

std::optional<PacketBoundary> ADMotionProtocolProcessor::find_position_boundary(const std::vector<uint8_t>& data, size_t start_pos) {
    // %%包的结构：%% + p[2](轴数) + p[3](数据类型) + 位置数据 + !!
    if (start_pos + 6 > data.size()) {  // 最小%%包大小：%% + 2个参数 + !! = 6字节
        std::cout << "%%包数据不足，需要至少6字节" << std::endl;
        return std::nullopt;
    }

    // 解析头部参数
    uint8_t axis_count = data[start_pos + 2];        // p[2] = 轴数
    uint8_t data_type = data[start_pos + 3];         // p[3] = 轴位置数据类型

    std::cout << "%%包参数: 轴数=" << (int)axis_count
              << ", 数据类型=" << (int)data_type;

    // 根据数据类型确定每个轴的数据大小
    size_t bytes_per_axis;
    switch (data_type) {
        case 0: bytes_per_axis = 4; std::cout << " (32位整数)"; break;  // 32位整数
        case 1: bytes_per_axis = 8; std::cout << " (64位整数)"; break;  // 64位整数
        case 2: bytes_per_axis = 4; std::cout << " (32位浮点数)"; break; // 32位浮点数
        case 3: bytes_per_axis = 8; std::cout << " (64位浮点数)"; break; // 64位浮点数
        default:
            std::cout << " (未知数据类型)" << std::endl;
            return std::nullopt;  // 未知数据类型
    }
    std::cout << std::endl;

    // 计算位置数据长度
    size_t position_data_length = axis_count * bytes_per_axis;

    // 计算总包长度：%% + 2个参数 + 位置数据 + !!
    size_t total_length = 2 + 2 + position_data_length + 2;

    std::cout << "%%包计算长度: " << total_length << " 字节" << std::endl;

    if (start_pos + total_length > data.size()) {
        std::cout << "%%包数据不足，需要 " << total_length << " 字节，实际只有 "
                  << (data.size() - start_pos) << " 字节" << std::endl;
        return std::nullopt;  // 数据不够
    }

    // 检查包尾!!
    if (data[start_pos + total_length - 2] == 0x21 &&
        data[start_pos + total_length - 1] == 0x21) {
        std::cout << "在位置 " << (start_pos + total_length - 2) << " 找到%%包尾 (0x21 0x21)" << std::endl;
        PacketBoundary boundary;
        boundary.start_pos = start_pos;
        boundary.packet_length = total_length;
        boundary.is_complete = true;
        boundary.type = PacketType::Position;
        return boundary;
    }

    std::cout << "%%包尾标识不匹配，期望!! (0x21 0x21)，实际: "
              << std::hex << (int)data[start_pos + total_length - 2] << " "
              << (int)data[start_pos + total_length - 1] << std::dec << std::endl;
    return std::nullopt;
}

std::optional<PacketBoundary> ADMotionProtocolProcessor::find_pdo_boundary(const std::vector<uint8_t>& data, size_t start_pos) {
    // ##包的结构：## + 12字节数据 + !!
    size_t total_length = 2 + 12 + 2;  // ## + 数据 + !!
    
    if (start_pos + total_length > data.size()) {
        return std::nullopt;
    }
    
    // 检查包尾!!
    if (check_packet_tail(data, start_pos + total_length - 2)) {
        PacketBoundary boundary;
        boundary.start_pos = start_pos;
        boundary.packet_length = total_length;
        boundary.is_complete = true;
        boundary.type = PacketType::PDO;
        return boundary;
    }
    
    return std::nullopt;
}

std::optional<ParsedPacket> ADMotionProtocolProcessor::parse_packet(const std::vector<uint8_t>& data, 
                                                                   const PacketBoundary& boundary) {
    if (boundary.start_pos + boundary.packet_length > data.size()) {
        return std::nullopt;
    }
    
    // 提取包数据（不包括包头和包尾标识）
    std::vector<uint8_t> packet_data;
    
    switch (boundary.type) {
        case PacketType::Command:
            // @@包：跳过@@，提取到!!之前的数据
            packet_data.assign(
                data.begin() + boundary.start_pos + 2,
                data.begin() + boundary.start_pos + boundary.packet_length - 2
            );
            break;
            
        case PacketType::IO:
            // $包：跳过$，提取到!!之前的数据
            packet_data.assign(
                data.begin() + boundary.start_pos + 2,
                data.begin() + boundary.start_pos + boundary.packet_length - 2
            );
            break;
            
        case PacketType::Position:
            // %%包：跳过%%，提取到!!之前的数据
            packet_data.assign(
                data.begin() + boundary.start_pos + 2,
                data.begin() + boundary.start_pos + boundary.packet_length - 2
            );
            break;
            
        case PacketType::PDO:
            // ##包：跳过##，提取12字节数据
            packet_data.assign(
                data.begin() + boundary.start_pos + 2,
                data.begin() + boundary.start_pos + boundary.packet_length - 2
            );
            break;
    }
    
    return ParsedPacket(boundary.type, std::move(packet_data), boundary.packet_length);
}

bool ADMotionProtocolProcessor::check_packet_tail(const std::vector<uint8_t>& data, size_t pos) {
    if (pos + 1 >= data.size()) {
        return false;
    }
    // 包尾是!! (0x21 0x21)，不是ASCII的'!'
    return data[pos] == 0x21 && data[pos + 1] == 0x21;
}

uint16_t ADMotionProtocolProcessor::calculate_checksum(const std::vector<uint8_t>& data, size_t start, size_t length) {
    uint16_t checksum = 0;
    for (size_t i = start; i < start + length && i < data.size(); ++i) {
        checksum += data[i];
    }
    return checksum;
}

void ADMotionProtocolProcessor::set_packet_handler(PacketHandler handler) {
    packet_handler_ = std::move(handler);
    std::cout << "包处理回调已设置" << std::endl;
}

void ADMotionProtocolProcessor::set_error_handler(ErrorHandler handler) {
    error_handler_ = std::move(handler);
    std::cout << "错误处理回调已设置" << std::endl;
}

std::optional<ParsedPacket> ADMotionProtocolProcessor::parse_complete_packet_data(const std::vector<uint8_t>& data) {
    if (data.size() < 4) {  // 最小包大小
        return std::nullopt;
    }

    // 检测包类型并解析
    if (data.size() >= 2) {
        // 检查@@包头 (Command)
        if (data[0] == 0x40 && data[1] == 0x40) {
            return parse_command_packet(data);
        }
        // 检查$$包头 (IO)
        else if (data[0] == 0x24 && data[1] == 0x24) {
            return parse_io_packet(data);
        }
        // 检查%%包头 (Position)
        else if (data[0] == 0x25 && data[1] == 0x25) {
            return parse_position_packet(data);
        }
        // 检查##包头 (PDO)
        else if (data[0] == 0x23 && data[1] == 0x23) {
            return parse_pdo_packet(data);
        }
    }

    std::cout << "未识别的包类型" << std::endl;
    return std::nullopt;
}

std::optional<ParsedPacket> ADMotionProtocolProcessor::parse_command_packet(const std::vector<uint8_t>& data) {
    // @@包的结构：@@ + p[2](short flag) + p[3](ArrayCount) + p[4](checksum) + p[5](tick) + 数据 + !!
    if (data.size() < 10) {  // 最小长度：@@ + 头部(6字节) + !! = 10字节
        return std::nullopt;
    }

    // 验证包头和包尾
    if (data[0] != 0x40 || data[1] != 0x40 ||
        data[data.size()-2] != 0x21 || data[data.size()-1] != 0x21) {
        return std::nullopt;
    }

    // 解析头部参数
    uint16_t flag = (data[3] << 8) | data[2];        // p[2] short flag（小端序）
    uint16_t array_count = (data[5] << 8) | data[4]; // p[3] ArrayCount（小端序）
    uint8_t checksum = data[6];                      // p[4] checksum
    uint8_t tick = data[7];                          // p[5] tick

    std::cout << "解析@@包: flag=0x" << std::hex << flag << std::dec
              << ", 数组长度=" << array_count
              << ", 校验和=0x" << std::hex << (int)checksum << std::dec
              << ", 指令索引=" << (int)tick << std::endl;

    // 验证数据长度
    size_t expected_data_length = array_count * 2;  // 每个数组元素2字节
    size_t actual_data_length = data.size() - 8 - 2;  // 总长度 - 头部8字节 - 包尾2字节

    if (actual_data_length != expected_data_length) {
        std::cout << "@@包数据长度不匹配，期望=" << expected_data_length
                  << "字节，实际=" << actual_data_length << "字节" << std::endl;
        return std::nullopt;
    }

    // 解析数据数组
    std::cout << "\n=== 命令数据数组 ===" << std::endl;
    for (size_t i = 0; i < array_count; i++) {
        size_t offset = 8 + i * 2;  // 跳过头部8字节
        uint16_t value = (data[offset + 1] << 8) | data[offset];  // 小端序
        std::cout << "数据[" << i << "]: " << value
                  << " (0x" << std::hex << std::setfill('0') << std::setw(4)
                  << value << std::dec << ")" << std::endl;
    }

    // 提取包数据（去除包头@@和包尾!!）
    std::vector<uint8_t> packet_data(data.begin() + 2, data.end() - 2);
    return ParsedPacket(PacketType::Command, std::move(packet_data), data.size());
}

std::optional<ParsedPacket> ADMotionProtocolProcessor::parse_io_packet(const std::vector<uint8_t>& data) {
    // $$包的结构：$$ + p[2](轴数) + p[3](每轴状态字节数) + p[4](输入组数) + p[5](输出组数) + 轴状态数据 + 输入IO数据 + 输出IO数据 + !!
    if (data.size() < 8) {  // 最小长度：$$ + 4个参数 + !! = 8字节
        return std::nullopt;
    }

    // 验证包头和包尾
    if (data[0] != 0x24 || data[1] != 0x24 ||
        data[data.size()-2] != 0x21 || data[data.size()-1] != 0x21) {
        return std::nullopt;
    }

    // 解析头部参数
    uint8_t axis_count = data[2];           // p[2] = 轴数
    uint8_t bytes_per_axis = data[3];       // p[3] = 每个轴状态占用字节数（一般是2字节=16位）
    uint8_t input_groups = data[4];         // p[4] = 输入口组数量（每组16位）
    uint8_t output_groups = data[5];        // p[5] = 输出口组数量（每组16位）

    std::cout << "解析$$包: 轴数=" << (int)axis_count
              << ", 每轴状态字节数=" << (int)bytes_per_axis
              << ", 输入组数=" << (int)input_groups
              << ", 输出组数=" << (int)output_groups << std::endl;

    // 计算各部分数据长度
    size_t axis_status_length = axis_count * bytes_per_axis;
    size_t input_data_length = input_groups * 2;
    size_t output_data_length = output_groups * 2;

    // 验证数据长度
    size_t expected_data_length = 4 + axis_status_length + input_data_length + output_data_length;
    size_t actual_data_length = data.size() - 2 - 2;  // 总长度 - 包头2字节 - 包尾2字节

    if (actual_data_length != expected_data_length) {
        std::cout << "$$包数据长度不匹配，期望=" << expected_data_length
                  << "字节，实际=" << actual_data_length << "字节" << std::endl;
        return std::nullopt;
    }

    // 解析各部分数据
    size_t offset = 6;  // 跳过包头$$ + 4个参数

    // 解析轴状态数据
    std::cout << "\n=== 轴状态数据 ===" << std::endl;
    for (int axis = 0; axis < axis_count; axis++) {
        if (bytes_per_axis == 2) {
            // 16位轴状态
            uint16_t axis_status = (data[offset + 1] << 8) | data[offset];  // 小端序
            std::cout << "轴" << axis << "状态: 0x" << std::hex << std::setfill('0')
                      << std::setw(4) << axis_status << std::dec << std::endl;
        } else {
            // 其他字节数的轴状态
            std::cout << "轴" << axis << "状态: ";
            for (int b = 0; b < bytes_per_axis; b++) {
                std::cout << std::hex << std::setfill('0') << std::setw(2)
                          << (int)data[offset + b] << " ";
            }
            std::cout << std::dec << std::endl;
        }
        offset += bytes_per_axis;
    }

    // 解析输入IO数据
    std::cout << "\n=== 数字输入IO ===" << std::endl;
    for (int group = 0; group < input_groups; group++) {
        uint16_t input_value = (data[offset + 1] << 8) | data[offset];  // 小端序
        std::cout << "输入组" << group << ": 0x" << std::hex << std::setfill('0')
                  << std::setw(4) << input_value << std::dec;

        // 显示各个位的状态
        std::cout << " (";
        for (int bit = 0; bit < 16; bit++) {
            if (input_value & (1 << bit)) {
                std::cout << bit << " ";
            }
        }
        std::cout << ")" << std::endl;
        offset += 2;
    }

    // 解析输出IO数据
    std::cout << "\n=== 数字输出IO ===" << std::endl;
    for (int group = 0; group < output_groups; group++) {
        uint16_t output_value = (data[offset + 1] << 8) | data[offset];  // 小端序
        std::cout << "输出组" << group << ": 0x" << std::hex << std::setfill('0')
                  << std::setw(4) << output_value << std::dec;

        // 显示各个位的状态
        std::cout << " (";
        for (int bit = 0; bit < 16; bit++) {
            if (output_value & (1 << bit)) {
                std::cout << bit << " ";
            }
        }
        std::cout << ")" << std::endl;
        offset += 2;
    }

    // 提取包数据（去除包头$$和包尾!!）
    std::vector<uint8_t> packet_data(data.begin() + 2, data.end() - 2);
    return ParsedPacket(PacketType::IO, std::move(packet_data), data.size());
}

std::optional<ParsedPacket> ADMotionProtocolProcessor::parse_position_packet(const std::vector<uint8_t>& data) {
    // %%包的结构：%% + p[2](轴数) + p[3](数据类型) + 位置数据 + !!
    if (data.size() < 6) {  // 最小长度：%% + 2个参数 + !! = 6字节
        return std::nullopt;
    }

    // 验证包头和包尾
    if (data[0] != 0x25 || data[1] != 0x25 ||
        data[data.size()-2] != 0x21 || data[data.size()-1] != 0x21) {
        return std::nullopt;
    }

    // 解析头部参数
    uint8_t axis_count = data[2];        // p[2] = 轴数
    uint8_t data_type = data[3];         // p[3] = 轴位置数据类型

    std::cout << "解析%%包: 轴数=" << (int)axis_count << ", 数据类型=" << (int)data_type;

    // 根据数据类型确定每个轴的数据大小
    size_t bytes_per_axis;
    const char* type_name;
    switch (data_type) {
        case 0: bytes_per_axis = 4; type_name = "32位整数"; break;
        case 1: bytes_per_axis = 8; type_name = "64位整数"; break;
        case 2: bytes_per_axis = 4; type_name = "32位浮点数"; break;
        case 3: bytes_per_axis = 8; type_name = "64位浮点数"; break;
        default:
            std::cout << " (未知数据类型)" << std::endl;
            return std::nullopt;
    }
    std::cout << " (" << type_name << ")" << std::endl;

    // 验证数据长度
    size_t expected_data_length = axis_count * bytes_per_axis;
    size_t actual_data_length = data.size() - 4 - 2;  // 总长度 - 头部4字节 - 包尾2字节

    if (actual_data_length != expected_data_length) {
        std::cout << "%%包数据长度不匹配，期望=" << expected_data_length
                  << "字节，实际=" << actual_data_length << "字节" << std::endl;
        return std::nullopt;
    }

    // 提取包数据（去除包头%%和包尾!!）
    std::vector<uint8_t> packet_data(data.begin() + 2, data.end() - 2);
    return ParsedPacket(PacketType::Position, std::move(packet_data), data.size());
}

std::optional<ParsedPacket> ADMotionProtocolProcessor::parse_pdo_packet(const std::vector<uint8_t>& data) {
    // ##包的结构：## + 12字节数据 + !! = 16字节
    if (data.size() != 16) {
        return std::nullopt;
    }

    // 验证包尾
    if (data[data.size()-2] != 0x21 || data[data.size()-1] != 0x21) {
        return std::nullopt;
    }

    // 提取包数据（去除包头##和包尾!!）
    std::vector<uint8_t> packet_data(data.begin() + 2, data.end() - 2);
    return ParsedPacket(PacketType::PDO, std::move(packet_data), data.size());
}

} // namespace ModernComm
