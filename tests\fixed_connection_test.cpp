/**
 * @file fixed_connection_test.cpp
 * @brief 修复后的连接测试，简化线程模型
 */

#include <iostream>
#include <asio.hpp>
#include <thread>
#include <chrono>
#include <future>
#include <atomic>

class SimpleAsioTransport {
private:
    asio::io_context io_context_;
    std::unique_ptr<asio::ip::tcp::socket> socket_;
    std::atomic<bool> connected_{false};
    std::vector<uint8_t> receive_buffer_;
    std::function<void(const std::vector<uint8_t>&)> receive_callback_;

public:
    SimpleAsioTransport() : receive_buffer_(1024) {
        socket_ = std::make_unique<asio::ip::tcp::socket>(io_context_);
        std::cout << "SimpleAsioTransport 构造函数" << std::endl;
    }
    
    ~SimpleAsioTransport() {
        disconnect();
        std::cout << "SimpleAsioTransport 析构函数" << std::endl;
    }
    
    void set_receive_callback(std::function<void(const std::vector<uint8_t>&)> callback) {
        receive_callback_ = std::move(callback);
    }
    
    std::future<bool> connect_async(const std::string& host, int port) {
        auto promise = std::make_shared<std::promise<bool>>();
        auto future = promise->get_future();
        
        // 在单独线程中执行连接，但不使用复杂的IO线程模型
        std::thread([this, host, port, promise]() {
            try {
                std::cout << "开始连接到 " << host << ":" << port << std::endl;
                
                // 重新创建socket（如果需要）
                if (!socket_ || !socket_->is_open()) {
                    socket_ = std::make_unique<asio::ip::tcp::socket>(io_context_);
                }
                
                // 创建endpoint
                asio::ip::tcp::endpoint endpoint(
                    asio::ip::address_v4::from_string(host), 
                    static_cast<unsigned short>(port)
                );
                
                // 同步连接 - 就像simple_asio_test一样
                std::error_code ec;
                socket_->connect(endpoint, ec);
                
                if (ec) {
                    std::cout << "连接失败: " << ec.value() << " - " << ec.message() << std::endl;
                    promise->set_value(false);
                } else {
                    std::cout << "✅ 连接成功!" << std::endl;
                    connected_.store(true);
                    
                    // 连接成功后启动接收
                    start_receive();
                    
                    // 启动IO线程来处理异步操作
                    start_io_thread();
                    
                    promise->set_value(true);
                }
                
            } catch (const std::exception& e) {
                std::cout << "连接异常: " << e.what() << std::endl;
                promise->set_value(false);
            }
        }).detach();
        
        return future;
    }
    
    void disconnect() {
        connected_.store(false);
        if (socket_ && socket_->is_open()) {
            std::error_code ec;
            socket_->close(ec);
            std::cout << "连接已断开" << std::endl;
        }
        io_context_.stop();
    }
    
    bool is_connected() const {
        return connected_.load();
    }

private:
    void start_receive() {
        if (!connected_.load() || !socket_ || !socket_->is_open()) {
            return;
        }
        
        socket_->async_read_some(
            asio::buffer(receive_buffer_),
            [this](const std::error_code& ec, size_t bytes_received) {
                if (!ec) {
                    std::cout << "收到 " << bytes_received << " 字节数据" << std::endl;
                    
                    if (receive_callback_) {
                        std::vector<uint8_t> data(
                            receive_buffer_.begin(), 
                            receive_buffer_.begin() + bytes_received
                        );
                        receive_callback_(data);
                    }
                    
                    // 继续接收
                    if (connected_.load()) {
                        start_receive();
                    }
                } else {
                    std::cout << "接收错误: " << ec.message() << std::endl;
                    connected_.store(false);
                }
            }
        );
    }
    
    void start_io_thread() {
        std::thread([this]() {
            std::cout << "IO线程启动" << std::endl;
            try {
                io_context_.run();
            } catch (const std::exception& e) {
                std::cout << "IO线程异常: " << e.what() << std::endl;
            }
            std::cout << "IO线程结束" << std::endl;
        }).detach();
    }
};

int main() {
    std::cout << "修复后的连接测试" << std::endl;
    std::cout << "===================" << std::endl;
    
    try {
        SimpleAsioTransport transport;
        
        // 设置接收回调
        transport.set_receive_callback([](const std::vector<uint8_t>& data) {
            std::cout << "接收回调: " << data.size() << " 字节" << std::endl;
            // 打印前16个字节
            for (size_t i = 0; i < std::min(data.size(), size_t(16)); ++i) {
                printf("%02X ", data[i]);
            }
            std::cout << std::endl;
        });
        
        // 尝试连接
        auto connect_future = transport.connect_async("192.168.2.2", 6666);
        
        // 等待连接结果
        std::cout << "等待连接结果（5秒超时）..." << std::endl;
        auto status = connect_future.wait_for(std::chrono::seconds(5));
        
        if (status == std::future_status::ready) {
            bool connected = connect_future.get();
            if (connected) {
                std::cout << "🎉 连接成功！" << std::endl;
                
                // 保持连接10秒，监听数据
                std::cout << "保持连接10秒，监听数据..." << std::endl;
                std::this_thread::sleep_for(std::chrono::seconds(10));
                
            } else {
                std::cout << "❌ 连接失败" << std::endl;
            }
        } else {
            std::cout << "⏱️  连接超时" << std::endl;
        }
        
        transport.disconnect();
        
    } catch (const std::exception& e) {
        std::cout << "异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "测试完成" << std::endl;
    return 0;
}