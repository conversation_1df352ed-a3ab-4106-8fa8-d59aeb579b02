/**
 * @file test_application_layer.cpp
 * @brief 应用层连接测试 - 连接***********:6666并解析数据
 */

#include "../include/communication_manager.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <iomanip>

using namespace ModernComm;

// 打印IO状态的辅助函数
void print_io_status(uint16_t status, int axis_num) {
    std::cout << "  轴" << axis_num << " IO状态: ";
    if (status & 0x01) std::cout << "[ENA] ";
    if (status & 0x02) std::cout << "[PEL] ";
    if (status & 0x04) std::cout << "[NEL] ";
    if (status & 0x08) std::cout << "[ORG] ";
    if (status & 0x10) std::cout << "[INP] ";
    if (status & 0x20) std::cout << "[MOV] ";
    if (status & 0x40) std::cout << "[ALM] ";
    if (status & 0x80) std::cout << "[EMG] ";
    if (status & 0x100) std::cout << "[RST] ";
    if (status & 0x200) std::cout << "[ANG] ";
    std::cout << "(0x" << std::hex << status << std::dec << ")" << std::endl;
}

// 解析并打印包数据
void parse_and_print_packet(const ParsedPacket& packet) {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::cout << "\n⏰ [" << std::put_time(std::localtime(&time_t), "%H:%M:%S") << "] ";
    
    switch (packet.type) {
        case PacketType::Command: {
            std::cout << "📦 命令响应包 (@@)" << std::endl;
            if (packet.data.size() >= 8) {
                // 解析TPci结构
                uint16_t flag = *reinterpret_cast<const uint16_t*>(&packet.data[0]);
                uint16_t array_count = *reinterpret_cast<const uint16_t*>(&packet.data[2]);
                uint16_t checksum = *reinterpret_cast<const uint16_t*>(&packet.data[4]);
                uint16_t tick = *reinterpret_cast<const uint16_t*>(&packet.data[6]);
                
                std::cout << "  标识: 0x" << std::hex << flag << std::dec << std::endl;
                std::cout << "  数组长度: " << array_count << std::endl;
                std::cout << "  校验和: 0x" << std::hex << checksum << std::dec << std::endl;
                std::cout << "  命令ID: " << tick << std::endl;
                
                // 解析命令数据
                if (packet.data.size() >= 8 + array_count * 2) {
                    std::cout << "  命令数据:" << std::endl;
                    for (int i = 0; i < array_count && (8 + i * 2 + 1) < packet.data.size(); ++i) {
                        int16_t cmd_value = *reinterpret_cast<const int16_t*>(&packet.data[8 + i * 2]);
                        std::cout << "    [" << i << "]: " << cmd_value << std::endl;
                    }
                }
            }
            break;
        }
        
        case PacketType::IO: {
            std::cout << "🔌 IO状态包 ($$)" << std::endl;
            if (packet.data.size() >= 6) {
                // 解析包头信息
                uint8_t axis_num = packet.data[2];
                uint8_t input_num = packet.data[3];
                uint8_t output_num = packet.data[4];
                
                std::cout << "  轴数量: " << (int)axis_num << std::endl;
                std::cout << "  输入组数: " << (int)input_num << std::endl;
                std::cout << "  输出组数: " << (int)output_num << std::endl;
                
                // 解析轴IO状态 (从第6字节开始)
                size_t offset = 6;
                if (packet.data.size() >= offset + axis_num * 4) {
                    std::cout << "  轴IO状态:" << std::endl;
                    for (int i = 0; i < axis_num && offset + 4 <= packet.data.size(); ++i) {
                        uint16_t status1 = *reinterpret_cast<const uint16_t*>(&packet.data[offset]);
                        uint16_t status2 = *reinterpret_cast<const uint16_t*>(&packet.data[offset + 2]);
                        print_io_status(status1, i);
                        offset += 4;
                    }
                }
                
                // 解析输入IO状态
                if (input_num > 0 && packet.data.size() >= offset + input_num * 2) {
                    std::cout << "  输入IO状态:" << std::endl;
                    for (int i = 0; i < input_num; ++i) {
                        uint16_t input_status = *reinterpret_cast<const uint16_t*>(&packet.data[offset]);
                        std::cout << "    输入组" << i << ": 0x" << std::hex << input_status << std::dec << std::endl;
                        offset += 2;
                    }
                }
                
                // 解析输出IO状态
                if (output_num > 0 && packet.data.size() >= offset + output_num * 2) {
                    std::cout << "  输出IO状态:" << std::endl;
                    for (int i = 0; i < output_num; ++i) {
                        uint16_t output_status = *reinterpret_cast<const uint16_t*>(&packet.data[offset]);
                        std::cout << "    输出组" << i << ": 0x" << std::hex << output_status << std::dec << std::endl;
                        offset += 2;
                    }
                }
            }
            break;
        }
        
        case PacketType::Position: {
            std::cout << "📍 位置数据包 (%%)" << std::endl;
            if (packet.data.size() >= 4) {
                uint8_t axis_num = packet.data[2];
                uint8_t pos_type = packet.data[3];
                
                std::cout << "  轴数量: " << (int)axis_num << std::endl;
                std::cout << "  位置类型: " << (int)pos_type << " (";
                switch (pos_type) {
                    case 0: std::cout << "32位整数"; break;
                    case 1: std::cout << "64位整数"; break;
                    case 2: std::cout << "32位浮点"; break;
                    case 3: std::cout << "64位浮点"; break;
                    default: std::cout << "未知"; break;
                }
                std::cout << ")" << std::endl;
                
                // 解析位置数据
                size_t pos_size = (pos_type == 1 || pos_type == 3) ? 8 : 4;
                size_t offset = 4;
                
                if (packet.data.size() >= offset + axis_num * pos_size) {
                    std::cout << "  轴位置:" << std::endl;
                    for (int i = 0; i < axis_num; ++i) {
                        if (pos_type == 0) { // 32位整数
                            int32_t pos = *reinterpret_cast<const int32_t*>(&packet.data[offset]);
                            std::cout << "    轴" << i << ": " << pos << std::endl;
                        } else if (pos_type == 2) { // 32位浮点
                            float pos = *reinterpret_cast<const float*>(&packet.data[offset]);
                            std::cout << "    轴" << i << ": " << pos << std::endl;
                        }
                        // 可以添加64位类型的处理
                        offset += pos_size;
                    }
                }
            }
            break;
        }
        
        case PacketType::PDO: {
            std::cout << "📡 PDO数据包 (##)" << std::endl;
            if (packet.data.size() >= 12) {
                uint32_t node_id = *reinterpret_cast<const uint32_t*>(&packet.data[0]);
                std::cout << "  节点ID: 0x" << std::hex << node_id << std::dec << std::endl;
                std::cout << "  PDO数据: ";
                for (int i = 4; i < 12 && i < packet.data.size(); ++i) {
                    printf("%02X ", packet.data[i]);
                }
                std::cout << std::endl;
            }
            break;
        }
        
        default:
            std::cout << "❓ 未知包类型: " << static_cast<int>(packet.type) << std::endl;
            break;
    }
    
    // 打印原始数据（前32字节）
    std::cout << "  原始数据 (" << packet.data.size() << " 字节): ";
    for (size_t i = 0; i < std::min(packet.data.size(), size_t(32)); ++i) {
        printf("%02X ", packet.data[i]);
    }
    if (packet.data.size() > 32) std::cout << "...";
    std::cout << std::endl;
}

int main() {
    std::cout << "=== ADMotion 应用层连接测试 ===" << std::endl;
    std::cout << "连接目标: ***********:6666" << std::endl;
    std::cout << "========================================" << std::endl;
    
    try {
        // 创建通讯管理器
        auto comm_manager = CommunicationFactory::create_asio_manager();
        
        // 设置包接收回调 - 解析并打印数据
        comm_manager->set_packet_callback([](ParsedPacket&& packet) {
            parse_and_print_packet(packet);
        });
        
        // 设置连接状态回调
        comm_manager->set_connection_callback([](bool connected) {
            if (connected) {
                std::cout << "✅ 设备连接成功！开始接收数据..." << std::endl;
            } else {
                std::cout << "❌ 设备连接断开" << std::endl;
            }
        });
        
        // 设置错误回调
        comm_manager->set_error_callback([](const std::string& error_msg) {
            std::cout << "⚠️  通讯错误: " << error_msg << std::endl;
        });
        
        // 尝试连接到设备
        std::cout << "正在连接到 ***********:6666..." << std::endl;
        auto connect_future = comm_manager->connect_async("***********", 6666);
        
        // 等待连接结果
        auto status = connect_future.wait_for(std::chrono::seconds(10));
        
        if (status == std::future_status::ready) {
            bool connected = connect_future.get();
            
            if (connected) {
                std::cout << "🎉 连接建立成功！" << std::endl;
                
                // 保持连接，接收数据60秒
                std::cout << "开始监听数据，持续60秒..." << std::endl;
                
                for (int i = 0; i < 60; ++i) {
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                    
                    // 每15秒显示一次统计信息
                    if ((i + 1) % 15 == 0) {
                        auto stats = comm_manager->get_statistics();
                        std::cout << "\n📊 [统计信息 - " << (i + 1) << "秒]" << std::endl;
                        std::cout << "接收字节数: " << stats.bytes_received << std::endl;
                        std::cout << "接收包数: " << stats.packets_received << std::endl;
                        std::cout << "发送字节数: " << stats.bytes_sent << std::endl;
                        std::cout << "发送包数: " << stats.packets_sent << std::endl;
                        std::cout << "========================================" << std::endl;
                    }
                    
                    // 检查连接状态
                    if (!comm_manager->is_connected()) {
                        std::cout << "连接已断开，退出监听" << std::endl;
                        break;
                    }
                }
                
                std::cout << "\n监听完成，断开连接..." << std::endl;
                comm_manager->disconnect();
                
            } else {
                std::cout << "❌ 连接失败" << std::endl;
            }
        } else {
            std::cout << "⏱️  连接超时（10秒）" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n测试完成" << std::endl;
    return 0;
}