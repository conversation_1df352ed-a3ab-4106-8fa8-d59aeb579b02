# ADMotion 通讯协议规范文档

## 1. 协议概述

### 1.1 协议架构
ADMotion通讯协议采用三层架构设计：
- **应用层**: ADMotion API接口 (`ADMotionPackage.h`)
- **协议层**: 包解析和数据处理 (`NetPackageRead.h`)  
- **传输层**: ASIO网络通讯 (`asiosocket.h`)

### 1.2 通讯模式
- **连接方式**: TCP/IP Socket
- **默认地址**: ***********:6666 或 ***********:6666
- **数据流向**: 双向通讯（命令/响应 + 主动上报）
- **并发模式**: 异步I/O + 队列处理

## 2. 数据包格式规范

### 2.1 通用包格式
```
[包头2字节] + [数据段N字节] + [包尾2字节]
```

### 2.2 包头类型定义
```cpp
enum ADMotionDataType {
    ADMC_CMD = 0,    // @@ - 运控回复上位机指令
    ADMC_IO = 1,     // $$ - 运控主动上传IO状态
    ADMC_POS = 2,    // %% - 运控主动上传轴位置
    ADMC_PDO = 3,    // ## - 运控主动上传PDO数据
};
```

### 2.3 包尾标识
所有数据包统一使用 `!!` (0x21 0x21) 作为包尾标识。

## 3. 详细包格式定义

### 3.1 命令响应包 (@@)
```cpp
// 包头: @@ (0x40 0x40)
// 数据段: TPci结构体
typedef struct TPci {
    unsigned short flag;        // 包标识 (保留)
    unsigned short ArrayCount;  // 数据数组长度
    unsigned short checksum;    // 校验和 (保留)
    unsigned short tick;        // 命令ID (请求/响应匹配)
    short data[COMMAND_DATA_LEN]; // 命令数据
    // data[0] = 命令类型
    // data[1~n] = 命令参数/返回值
} TPci;

// 总长度计算: 2 + 8 + ArrayCount*2 + 2 = 12 + ArrayCount*2
```

### 3.2 IO状态包 ($$)
```cpp
// 包头: $$ (0x24 0x24) + 控制字节
// 格式: $$ + AxisNum + InputGroupNum + OutputGroupNum + Reserved
struct IOPacketHeader {
    char header[2];           // $$
    unsigned char axis_num;   // 轴数量
    unsigned char input_num;  // 输入组数量 (每组16个)
    unsigned char output_num; // 输出组数量 (每组16个)
    unsigned char reserved;   // 保留字节
};

// 数据段: TCardIO结构体
typedef struct TCardIO {
    volatile uint16_t positionCrd[4][2]; // 轴IO状态 (4轴×2字节)
    volatile uint16_t Input[4];          // 输入IO状态 (4组×16位)
    volatile uint16_t Output[4];         // 输出IO状态 (4组×16位)
} TCardIO;

// 轴IO状态位定义:
// Bit 0: ENA (使能)     Bit 1: PEL (正限位)   Bit 2: NEL (负限位)
// Bit 3: ORG (原点)     Bit 4: INP (到位)     Bit 5: MOV (移动中)
// Bit 6: ALM (报警)     Bit 7: EMG (急停)     Bit 8: RST (复位中)
// Bit 9: ANG (角度识别) Bit 10: 高压信号      Bit 11: 高压使能
```

### 3.3 位置数据包 (%%)
```cpp
// 包头: %% (0x25 0x25) + 控制字节
struct PositionPacketHeader {
    char header[2];              // %%
    unsigned char axis_num;      // 轴数量
    unsigned char pos_type;      // 位置数据类型
    // pos_type: 0=32位整数, 1=64位整数, 2=32位浮点, 3=64位浮点
};

// 数据段: 轴位置数组
// 长度 = axis_num * sizeof(position_type)
typedef struct TCardAxisPos {
    volatile int32_t X1Pos;  // 轴0当前位置
    volatile int32_t Y1Pos;  // 轴1当前位置
    volatile int32_t X2Pos;  // 轴2当前位置
    volatile int32_t Y2Pos;  // 轴3当前位置
    // ... 可扩展到更多轴
} TCardAxisPos;
```

### 3.4 PDO数据包 (##)
```cpp
// 包头: ## (0x23 0x23)
// 数据段: 固定12字节PDO数据
struct PDOData {
    uint32_t node_id;    // 节点ID (含Frame号)
    uint8_t data[8];     // 8字节数据 (按协议格式解释)
};
```

## 4. 粘包处理机制

### 4.1 粘包问题
TCP流式传输可能导致：
- **包分割**: 一个完整包被分成多次接收
- **包合并**: 多个包在一次接收中合并
- **包错位**: 包边界不对齐

### 4.2 解决方案
```cpp
template<class T>
class TNetPackageReader {
private:
    std::string m_data;  // 累积缓冲区
    T Pack;              // 包定义对象
    
public:
    void Read(const std::string& text) {
        m_data += text;  // 累积数据
        
        // 循环解析完整包
        while (true) {
            // 1. 查找包头
            int head_pos = Pack.FindHead(m_data.data(), m_data.size(), Pack.Head);
            if (head_pos < 0) break;  // 未找到包头
            
            // 2. 计算包长度
            int packet_len = Pack.HeadSize + Pack.Head.datalen + Pack.EndSize;
            if (m_data.size() < head_pos + packet_len) break;  // 包不完整
            
            // 3. 验证包尾
            const char* packet_end = m_data.data() + head_pos + packet_len - Pack.EndSize;
            if (Pack.IsEndChar(packet_end) == 0) {
                // 包完整且有效，提取数据
                std::string packet_data(m_data.data() + head_pos + Pack.HeadSize, 
                                       Pack.Head.datalen);
                Pack.pOnReadData(Pack.Head, packet_data, Pack.TagCall);
            }
            
            // 4. 移除已处理的数据
            m_data.erase(0, head_pos + packet_len);
        }
    }
};
```

### 4.3 包边界检测算法
```cpp
int TPackage_Motion::FindHead(const unsigned char* buff, int len, HEAD_Motion& Head) {
    for (int i = 0; i < len - 1; i++) {
        if (buff[i] == '@' && buff[i+1] == '@') {
            // 解析命令响应包头
            if (len - i >= 10) {
                unsigned short* pShort = (unsigned short*)(buff + i + 2);
                Head.type = ADMotionDataType::ADMC_CMD;
                Head.flag = pShort[0];
                Head.ArrayCount = pShort[1];
                Head.checksum = pShort[2];
                Head.tick = pShort[3];
                Head.datalen = Head.ArrayCount * sizeof(short) + 8;
                return i;
            }
        }
        else if (buff[i] == '$' && buff[i+1] == '$') {
            // 解析IO状态包头
            Head.type = ADMotionDataType::ADMC_IO;
            if (len - i > 5) {
                Head.AxisIO_num = buff[i+2];
                Head.IO_inputNum = buff[i+3];
                Head.IO_outputNum = buff[i+4];
                Head.datalen = Head.AxisIO_num*2 + Head.IO_inputNum*2 + Head.IO_outputNum*2 + 4;
            }
            return i;
        }
        else if (buff[i] == '%' && buff[i+1] == '%') {
            // 解析位置数据包头
            Head.type = ADMotionDataType::ADMC_POS;
            if (len - i > 3) {
                Head.Axis_num = buff[i+2];
                Head.AxisPosType = buff[i+3];
                switch (Head.AxisPosType) {
                    case 0: Head.datalen = Head.Axis_num * 4 + 2; break;  // 32位整数
                    case 1: Head.datalen = Head.Axis_num * 8 + 2; break;  // 64位整数
                    case 2: Head.datalen = Head.Axis_num * 4 + 2; break;  // 32位浮点
                    case 3: Head.datalen = Head.Axis_num * 8 + 2; break;  // 64位浮点
                }
            }
            return i;
        }
        else if (buff[i] == '#' && buff[i+1] == '#') {
            // 解析PDO数据包头
            Head.type = ADMotionDataType::ADMC_PDO;
            Head.datalen = 12;  // 固定12字节
            return i;
        }
    }
    return -1;  // 未找到有效包头
}
```

## 5. 通讯流程分析

### 5.1 发送流程
```cpp
// 1. API调用 -> 命令构建
short API_OpenBoard(TADMotionConn* handle, const char* ip, int port) {
    unsigned short tick = GetIndex(handle);           // 获取命令ID
    CommandInitial(handle, tick, CMD_OPEN_BOARD);     // 初始化命令
    Add16ToBuff(handle, tick, param1);                // 添加参数
    Add32ToBuff(handle, tick, param2);
    
    // 2. 命令发送
    short ret = SendCommand(handle, tick);            // 发送到网络
    
    // 3. 等待响应
    SemWait(&handle->CardData->pctrl->sema);          // 等待信号量
    
    // 4. 获取结果
    Get16FromBuff(handle, tick, &result);
    CommandUninitial(handle, tick);                   // 清理资源
    return result;
}

// 底层发送实现
short SendCommand(TADMotionConn* handle, unsigned short tick) {
    TCardData* pCard = handle->CardData;
    TCommandBuffer& cmd = pCard->gCommandBuffer[tick];
    
    // 构建TPci包
    TPci packet;
    packet.flag = 0;
    packet.ArrayCount = cmd.dataCount;
    packet.checksum = 0;
    packet.tick = tick;
    memcpy(packet.data, cmd.data, cmd.dataCount * sizeof(short));
    
    // 构建完整数据包: @@ + TPci + !!
    std::string packet_data = "@@";
    packet_data.append((char*)&packet, sizeof(packet));
    packet_data.append("!!");
    
    // 通过Socket发送
    return handle->write(packet_data.data(), packet_data.size());
}
```

### 5.2 接收流程
```cpp
// 1. 网络接收 (TAsioSocketBase)
void TAsioSocketBase::on_read(const std::error_code& error, size_t bytes_transferred) {
    if (!error) {
        std::string received_data(read_data, bytes_transferred);
        
        // 2. 回调到队列处理 (TAsioSocket_Queue)
        if (m_SocketRead) {
            m_SocketRead(received_data, m_SocketReadTag);
        }
        
        // 继续异步读取
        start();
    }
}

// 3. 队列处理 (TNetPackageQueue)
void TNetPackageQueue::PackagePush(const std::string& str) {
    std::lock_guard<std::mutex> lock(mutex_vec);
    m_Packages.push(str);
    
    // 队列满时丢弃旧包
    if (m_Packages.size() > QueueMaxSize) {
        m_Packages.pop();
    }
    
    // 启动处理线程
    if (pthread == nullptr) {
        pthread = new std::thread([&]() { this->ThreadRead(); });
    } else {
        SemPost(&m_sem);  // 通知处理线程
    }
}

// 4. 包解析 (TNetPackageReader)
void TNetPackageReader::Read(const std::string& text) {
    // 粘包处理逻辑 (见4.2节)
    // ...
    
    // 解析完成后回调
    if (Pack.pOnReadData) {
        Pack.pOnReadData(Pack.Head, packet_data, Pack.TagCall);
    }
}

// 5. 数据处理 (TPackage_Motion::ReadPackage)
void TPackage_Motion::ReadPackage(const HEAD_Motion& Head, std::string& data, void* CAllTag) {
    TADMotionConn* pADMotion = (TADMotionConn*)CAllTag;
    TCardData* pCard = pADMotion->CardData;
    
    switch (Head.type) {
        case ADMotionDataType::ADMC_CMD:
            // 命令响应处理
            memcpy(&pCard->gPci, data.data(), sizeof(TPci));
            SemPost(&pCard->pctrl->sema);  // 唤醒等待线程
            break;
            
        case ADMotionDataType::ADMC_IO:
            // IO状态更新
            memcpy(&pCard->gCardIO, data.data(), sizeof(TCardIO));
            if (pADMotion->OnIOChange) {
                pADMotion->OnIOChange(pADMotion, Head, oldIO, newIO, pADMotion->Event_Tag);
            }
            break;
            
        case ADMotionDataType::ADMC_POS:
            // 位置数据更新
            memcpy(&pCard->gCardAxisPos, data.data(), sizeof(TCardAxisPos));
            if (pADMotion->callback_data) {
                pADMotion->callback_data(pADMotion, Head, data, pADMotion->Event_Tag);
            }
            break;
            
        case ADMotionDataType::ADMC_PDO:
            // PDO数据处理
            if (pADMotion->callback_data) {
                pADMotion->callback_data(pADMotion, Head, data, pADMotion->Event_Tag);
            }
            break;
    }
}
```

## 6. 同步/异步处理机制

### 6.1 同步命令处理
```cpp
// 使用信号量实现同步等待
struct TCardData_cplusplus {
    semDATA sema;                    // 信号量
    std::mutex socketmutex;          // Socket互斥锁
    std::atomic_ushort g_postindex;  // 原子操作的包ID
};

// 同步调用流程
short SyncCommand(TADMotionConn* handle, unsigned short cmd, ...) {
    unsigned short tick = GetIndex(handle);  // 原子获取唯一ID
    
    // 构建命令
    CommandInitial(handle, tick, cmd);
    // ... 添加参数
    
    // 发送命令
    SendCommand(handle, tick);
    
    // 等待响应 (最多5秒超时)
    if (SemWaitTimeout(&handle->CardData->pctrl->sema, 5000) == 0) {
        // 获取结果
        return GetResultFromBuff(handle, tick);
    } else {
        return CMD_TIMEOUT;  // 超时错误
    }
}
```

### 6.2 异步数据上报
```cpp
// IO状态和位置数据通过回调异步处理
class TADMotionConn {
public:
    Event_MCDataRecieved callback_data = nullptr;   // 数据回调
    Event_MCIOChange OnIOChange = nullptr;          // IO变化回调
    
    void setCallback(Event_MCDataRecieved callback) {
        callback_data = callback;
    }
};

// 用户注册回调
void OnMotionDataReceived(TADMotionConn* conn, const HEAD_Motion& head, 
                         const std::string& data, void* tag) {
    switch (head.type) {
        case ADMotionDataType::ADMC_POS:
            // 处理位置数据
            updatePositionDisplay(data);
            break;
        case ADMotionDataType::ADMC_PDO:
            // 处理PDO数据
            processPDOData(data);
            break;
    }
}

motion_conn.setCallback(OnMotionDataReceived);
```

## 7. 错误检测和恢复机制

### 7.1 多层错误检测
```cpp
// 1. 参数验证层
short API_SetAxisPrm(TADMotionConn* handle, short crd, ...) {
    if (!handle || !handle->CardData) return CMD_HANDLE_ERROR;
    if (crd < 0 || crd >= MAX_CRD) return CMD_CRD_NUM_ERROR;
    // ... 其他参数检查
}

// 2. 网络传输层
void TAsioSocketBase::on_read(const std::error_code& error, size_t bytes_transferred) {
    if (error) {
        DoSocketEvent(SOCKET_EVENT_ERROR);  // 触发错误事件
        return;
    }
    // ... 正常处理
}

// 3. 协议解析层
int TPackage_Motion::FindHead(const unsigned char* buff, int len, HEAD_Motion& Head) {
    // 包头验证
    if (Head.ArrayCount > COMMAND_DATA_LEN) {
        return -1;  // 数据长度异常
    }
    // ... 其他验证
}

// 4. 包尾验证
int TPackage_Motion::IsEndChar(const unsigned char* pEnd) {
    return (pEnd[0] == '!' && pEnd[1] == '!') ? 0 : 0xffff;
}
```

### 7.2 错误恢复策略
```cpp
// 1. 连接断开重连
class TADMotionConn {
    bool auto_reconnect = true;
    int reconnect_interval = 1000;  // ms
    
    void OnConnectionLost() {
        if (auto_reconnect) {
            std::thread([this]() {
                std::this_thread::sleep_for(std::chrono::milliseconds(reconnect_interval));
                Connection(m_ip, m_port);  // 尝试重连
            }).detach();
        }
    }
};

// 2. 包错误处理
void TNetPackageReader::Read(const std::string& text) {
    // ... 包解析逻辑
    
    if (packet_error) {
        // 记录错误包
        std::string errStr = GetB16Str(error_data, error_len);
        ADSOCKET::_ThreadPostMessage(MT_WARN, MD_NET, 0, 
            "收到错误包: " + errStr, nullptr);
        
        // 丢弃错误数据，继续处理
        m_data.erase(0, error_pos + 1);
    }
}

// 3. 队列溢出保护
void TNetPackageQueue::PackagePush(const std::string& str) {
    std::lock_guard<std::mutex> lock(mutex_vec);
    m_Packages.push(str);
    
    if (m_Packages.size() > QueueMaxSize) {
        m_Packages.pop();  // 丢弃最旧的包
        ADSOCKET::_ThreadPostMessage(MT_ERROR, MD_NET, 0,
            "队列溢出，丢弃旧包", nullptr);
    }
}
```

## 8. 数据结构映射关系

### 8.1 核心数据结构层次
```cpp
// 顶层: 运动控制卡数据
struct TCardData {
    TCardData_cplusplus* pctrl;           // C++控制对象
    TCardInfo CardInfo;                   // 卡基本信息
    TCardAxisPos gCardAxisPos;            // 轴位置数据
    TCardIO gCardIO;                      // IO状态数据
    TCommandBuffer gCommandBuffer[1024];  // 命令缓冲区数组
    TCrdApiPrm gCrdApiPrm[MAX_CRD];      // 坐标系参数
    TLookAheadPrm gLookAheadPrm[MAX_CRD]; // 前瞻算法参数
    TPci gPci;                           // 当前PCI通讯包
};

// 中层: 协议包头结构
struct HEAD_Motion {
    ADMotionDataType type;    // 包类型
    int datalen;             // 数据长度

    // 命令包专用字段
    unsigned short flag;
    unsigned short ArrayCount;
    unsigned short checksum;
    unsigned short tick;

    // IO包专用字段
    unsigned char AxisIO_num;
    unsigned char IO_inputNum;
    unsigned char IO_outputNum;
    unsigned char IOChange;

    // 位置包专用字段
    unsigned char Axis_num;
    unsigned char AxisPosType;
};

// 底层: 网络传输结构
class TNetPackageReader<TPackage_Motion> {
    std::string m_data;           // 粘包缓冲区
    TPackage_Motion Pack;         // 包定义
};
```

### 8.2 数据转换规则
```cpp
// 1. 网络字节序转换 (小端序)
template<typename T>
T NetworkToHost(const uint8_t* data) {
    T result;
    memcpy(&result, data, sizeof(T));
    return result;  // ADMotion协议使用小端序，与x86一致
}

// 2. 类型安全的数据提取
class SafeDataExtractor {
public:
    template<typename T>
    static bool Extract(const std::string& data, size_t offset, T& result) {
        if (offset + sizeof(T) > data.size()) {
            return false;  // 数据不足
        }
        memcpy(&result, data.data() + offset, sizeof(T));
        return true;
    }

    // 专用于ADMotion协议的提取函数
    static bool ExtractTPci(const std::string& data, TPci& pci) {
        if (data.size() < 8) return false;

        const uint16_t* pShort = reinterpret_cast<const uint16_t*>(data.data());
        pci.flag = pShort[0];
        pci.ArrayCount = pShort[1];
        pci.checksum = pShort[2];
        pci.tick = pShort[3];

        if (data.size() < 8 + pci.ArrayCount * sizeof(short)) {
            return false;
        }

        memcpy(pci.data, data.data() + 8, pci.ArrayCount * sizeof(short));
        return true;
    }
};

// 3. 命令/响应映射
enum MotionCommand {
    CMD_OPEN_BOARD = 1,
    CMD_CLOSE_BOARD = 2,
    CMD_AXIS_ON = 10,
    CMD_AXIS_OFF = 11,
    CMD_SET_JOG_MODE = 20,
    CMD_JOG_UPDATE = 21,
    CMD_GET_AXIS_POS = 30,
    CMD_GET_CRD_POS = 31,
    // ... 更多命令
};

struct CommandResponseMap {
    MotionCommand command;
    size_t request_params;   // 请求参数数量
    size_t response_params;  // 响应参数数量
    bool has_return_value;   // 是否有返回值
};

static const CommandResponseMap g_CommandMap[] = {
    {CMD_OPEN_BOARD, 2, 1, true},      // 请求: ip+port, 响应: result
    {CMD_AXIS_ON, 1, 1, true},         // 请求: axis, 响应: result
    {CMD_GET_AXIS_POS, 1, 2, true},    // 请求: axis, 响应: result+pos
    // ...
};
```

### 8.3 内存布局优化
```cpp
// 当前布局问题分析
struct TCardData_Current {
    // 问题1: 指针和数据混合，缓存不友好
    TCardData_cplusplus* pctrl;     // 8字节指针
    TCardInfo CardInfo;             // 140字节结构
    TCardAxisPos gCardAxisPos;      // 16字节
    TCardIO gCardIO;                // 24字节
    // 问题2: 大数组导致内存浪费
    TCommandBuffer gCommandBuffer[1024]; // 1024 * 208字节 = 208KB
};

// 优化后的布局设计
struct OptimizedCardData {
    // 1. 热点数据集中
    struct HotData {
        TCardAxisPos current_pos;    // 当前位置 (频繁访问)
        TCardIO current_io;          // 当前IO状态 (频繁访问)
        std::atomic<uint32_t> seq_num; // 序列号 (线程安全)
    } hot_data;

    // 2. 冷数据分离
    struct ColdData {
        TCardInfo card_info;         // 卡信息 (初始化后很少变化)
        TCrdApiPrm crd_params[MAX_CRD]; // 坐标系参数 (配置数据)
    } cold_data;

    // 3. 动态分配的缓冲区
    std::unique_ptr<CommandBufferPool> command_pool;
    std::unique_ptr<LookAheadBufferPool> lookahead_pool;

    // 4. 同步原语
    mutable std::shared_mutex data_mutex;  // 读写锁
    std::condition_variable_any data_cv;   // 条件变量
};
```

## 9. 抽象通讯接口规范

### 9.1 现代化接口设计
```cpp
namespace ADMotion::Modern {

// 1. 类型安全的包定义
template<ADMotionDataType PacketType>
struct PacketTraits;

template<>
struct PacketTraits<ADMotionDataType::ADMC_CMD> {
    using HeaderType = TPci;
    using DataType = std::vector<int16_t>;
    static constexpr const char* PacketHeader = "@@";
    static constexpr size_t MinHeaderSize = 8;
};

template<>
struct PacketTraits<ADMotionDataType::ADMC_IO> {
    using HeaderType = IOPacketHeader;
    using DataType = TCardIO;
    static constexpr const char* PacketHeader = "$$";
    static constexpr size_t MinHeaderSize = 6;
};

// 2. 现代化的包解析器
template<ADMotionDataType PacketType>
class TypedPacketParser {
private:
    using Traits = PacketTraits<PacketType>;
    std::vector<uint8_t> buffer_;
    std::function<void(typename Traits::HeaderType, typename Traits::DataType)> handler_;

public:
    void setHandler(auto&& handler) {
        handler_ = std::forward<decltype(handler)>(handler);
    }

    ParseResult processData(std::span<const uint8_t> data) {
        buffer_.insert(buffer_.end(), data.begin(), data.end());

        while (auto packet = tryExtractPacket()) {
            if (handler_) {
                handler_(packet->header, packet->data);
            }
        }

        return ParseResult::Success;
    }

private:
    std::optional<Packet<PacketType>> tryExtractPacket() {
        // 使用ASIO 1.30的边界检测
        auto boundary = findPacketBoundary(buffer_);
        if (!boundary) return std::nullopt;

        auto packet = parsePacket<PacketType>(
            buffer_.data() + boundary->start,
            boundary->length);

        // 移除已处理的数据
        buffer_.erase(buffer_.begin(), buffer_.begin() + boundary->end);

        return packet;
    }
};

// 3. 异步通讯管理器
class AsyncCommunicationManager {
private:
    asio::io_context io_context_;
    std::unique_ptr<asio::ip::tcp::socket> socket_;
    std::unordered_map<uint16_t, std::promise<CommandResponse>> pending_commands_;

public:
    // C++14 auto返回类型推导
    auto connectAsync(std::string_view host, uint16_t port)
        -> std::future<std::error_code> {

        auto promise = std::make_shared<std::promise<std::error_code>>();
        auto future = promise->get_future();

        asio::ip::tcp::endpoint endpoint(
            asio::ip::address::from_string(std::string(host)), port);

        socket_->async_connect(endpoint,
            [promise](const std::error_code& ec) {
                promise->set_value(ec);
            });

        return future;
    }

    // C++17 if constexpr用于编译时分支
    template<typename CommandType>
    auto sendCommandAsync(CommandType&& command)
        -> std::future<typename CommandType::ResponseType> {

        auto tick = generateTick();
        auto promise = std::make_shared<std::promise<typename CommandType::ResponseType>>();
        auto future = promise->get_future();

        // 注册待处理命令
        pending_commands_[tick] = std::move(*promise);

        // 序列化命令
        auto packet_data = serializeCommand(std::forward<CommandType>(command), tick);

        // 异步发送
        asio::async_write(*socket_, asio::buffer(packet_data),
            [this, tick](const std::error_code& ec, size_t bytes_transferred) {
                if (ec) {
                    completeCommandWithError(tick, ec);
                }
            });

        return future;
    }

    // 设置数据回调 (支持lambda)
    template<typename Handler>
    void setDataHandler(Handler&& handler) {
        data_handler_ = std::forward<Handler>(handler);
    }

private:
    std::function<void(ADMotionDataType, std::span<const uint8_t>)> data_handler_;
    std::atomic<uint16_t> tick_counter_{1};

    uint16_t generateTick() {
        return tick_counter_++;
    }
};

// 4. 强类型的命令定义
struct AxisOnCommand {
    using ResponseType = CommandResult;

    uint16_t axis;

    static constexpr MotionCommand CommandId = CMD_AXIS_ON;
    static constexpr size_t ParameterCount = 1;
};

struct GetAxisPositionCommand {
    using ResponseType = AxisPosition;

    uint16_t axis;

    static constexpr MotionCommand CommandId = CMD_GET_AXIS_POS;
    static constexpr size_t ParameterCount = 1;
};

// 5. 类型安全的响应处理
struct CommandResult {
    int16_t error_code;
    bool success() const { return error_code >= 0; }
    std::string errorMessage() const;
};

struct AxisPosition {
    int16_t error_code;
    double position;

    bool success() const { return error_code >= 0; }
};

// 6. 现代化的API接口
class ModernMotionAPI {
private:
    std::unique_ptr<AsyncCommunicationManager> comm_manager_;

public:
    ModernMotionAPI() : comm_manager_(std::make_unique<AsyncCommunicationManager>()) {
        // 设置数据处理回调
        comm_manager_->setDataHandler([this](ADMotionDataType type, std::span<const uint8_t> data) {
            handleIncomingData(type, data);
        });
    }

    // 异步连接
    std::future<void> connectAsync(std::string_view host, uint16_t port) {
        return comm_manager_->connectAsync(host, port)
            .then([](std::error_code ec) {
                if (ec) {
                    throw CommunicationException("Connection failed: " + ec.message());
                }
            });
    }

    // 异步轴控制
    std::future<void> enableAxisAsync(uint16_t axis) {
        AxisOnCommand command{axis};
        return comm_manager_->sendCommandAsync(std::move(command))
            .then([](CommandResult result) {
                if (!result.success()) {
                    throw MotionException("Axis enable failed: " + result.errorMessage());
                }
            });
    }

    // 异步位置查询
    std::future<double> getAxisPositionAsync(uint16_t axis) {
        GetAxisPositionCommand command{axis};
        return comm_manager_->sendCommandAsync(std::move(command))
            .then([](AxisPosition result) -> double {
                if (!result.success()) {
                    throw MotionException("Position query failed");
                }
                return result.position;
            });
    }

    // 事件处理 (C++14 泛型lambda)
    template<typename Handler>
    void onIOChanged(Handler&& handler) {
        io_change_handlers_.emplace_back(std::forward<Handler>(handler));
    }

    template<typename Handler>
    void onPositionUpdated(Handler&& handler) {
        position_update_handlers_.emplace_back(std::forward<Handler>(handler));
    }

private:
    std::vector<std::function<void(const TCardIO&)>> io_change_handlers_;
    std::vector<std::function<void(const TCardAxisPos&)>> position_update_handlers_;

    void handleIncomingData(ADMotionDataType type, std::span<const uint8_t> data) {
        switch (type) {
            case ADMotionDataType::ADMC_IO: {
                TCardIO io_data;
                if (SafeDataExtractor::Extract(data, 0, io_data)) {
                    for (auto& handler : io_change_handlers_) {
                        handler(io_data);
                    }
                }
                break;
            }
            case ADMotionDataType::ADMC_POS: {
                TCardAxisPos pos_data;
                if (SafeDataExtractor::Extract(data, 0, pos_data)) {
                    for (auto& handler : position_update_handlers_) {
                        handler(pos_data);
                    }
                }
                break;
            }
        }
    }
};

} // namespace ADMotion::Modern
```

## 10. 重构指导原则

### 10.1 兼容性保证
1. **保持C接口不变**: 确保现有用户代码无需修改
2. **数据结构兼容**: 保持关键结构体的内存布局
3. **协议完全兼容**: 网络协议保持100%兼容
4. **渐进式迁移**: 允许新旧接口并存

### 10.2 现代化改进
1. **类型安全**: 使用强类型和模板消除类型转换错误
2. **内存安全**: 智能指针和RAII管理资源
3. **异常安全**: 现代异常处理机制
4. **并发安全**: 现代并发原语和无锁数据结构

### 10.3 性能优化
1. **零拷贝**: 使用移动语义和std::span避免不必要拷贝
2. **缓存友好**: 优化数据结构布局提高缓存命中率
3. **异步I/O**: 充分利用ASIO 1.30的异步特性
4. **编译时优化**: 使用constexpr和模板元编程

这个协议规范文档为ADMotion通讯库的现代化重构提供了完整的技术基础和设计指导。
