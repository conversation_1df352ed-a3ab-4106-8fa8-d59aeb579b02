/**
 * @file test_async.cpp
 * @brief 异步编程基础学习
 * @details 学习 std::future, std::promise, std::async 等异步编程概念
 */

#include <iostream>
#include <future>     // std::future, std::promise, std::async
#include <thread>     // std::thread
#include <chrono>     // 时间相关
#include <string>

// 模拟一个耗时的网络连接操作
std::string simulate_network_connect(const std::string& host, int delay_ms) {
    std::cout << "开始连接到 " << host << "..." << std::endl;
    
    // 模拟网络延迟
    std::this_thread::sleep_for(std::chrono::milliseconds(delay_ms));
    
    std::cout << "连接到 " << host << " 完成！" << std::endl;
    return "连接成功: " + host;
}

// 演示传统同步方式的问题
void demo_synchronous_problem() {
    std::cout << "\n=== 传统同步方式的问题 ===" << std::endl;
    
    auto start = std::chrono::steady_clock::now();
    
    // 连接三个服务器，每个需要1秒
    std::string result1 = simulate_network_connect("服务器1", 1000);
    std::string result2 = simulate_network_connect("服务器2", 1000);
    std::string result3 = simulate_network_connect("服务器3", 1000);
    
    auto end = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    std::cout << "同步方式总耗时: " << duration.count() << "ms" << std::endl;
    std::cout << "问题：必须等待每个操作完成才能开始下一个！" << std::endl;
}

// 演示 std::async 异步执行
void demo_async_basic() {
    std::cout << "\n=== std::async 异步执行 ===" << std::endl;
    
    auto start = std::chrono::steady_clock::now();
    
    // 使用 std::async 异步启动三个连接操作
    auto future1 = std::async(std::launch::async, simulate_network_connect, "异步服务器1", 1000);
    auto future2 = std::async(std::launch::async, simulate_network_connect, "异步服务器2", 1000);
    auto future3 = std::async(std::launch::async, simulate_network_connect, "异步服务器3", 1000);
    
    std::cout << "三个异步操作已启动，现在可以做其他事情..." << std::endl;
    
    // 获取结果（这里会等待操作完成）
    std::string result1 = future1.get();
    std::string result2 = future2.get();
    std::string result3 = future3.get();
    
    auto end = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    std::cout << "异步方式总耗时: " << duration.count() << "ms" << std::endl;
    std::cout << "优势：三个操作并行执行，总时间大大减少！" << std::endl;
}

// 演示 std::promise 和 std::future 的手动控制
void demo_promise_future() {
    std::cout << "\n=== std::promise 和 std::future 手动控制 ===" << std::endl;
    
    // 创建 promise 和 future 对
    std::promise<std::string> promise;
    std::future<std::string> future = promise.get_future();
    
    // 在另一个线程中执行异步操作
    std::thread worker([&promise]() {
        std::cout << "工作线程：开始处理任务..." << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(2000));
        
        // 设置结果
        promise.set_value("任务完成！");
        std::cout << "工作线程：任务结果已设置" << std::endl;
    });
    
    std::cout << "主线程：可以做其他事情..." << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    std::cout << "主线程：做了一些其他工作" << std::endl;
    
    // 等待结果
    std::cout << "主线程：等待工作线程的结果..." << std::endl;
    std::string result = future.get();  // 这里会阻塞直到有结果
    
    std::cout << "主线程：收到结果: " << result << std::endl;
    
    worker.join();  // 等待工作线程结束
}

int main() {
    std::cout << "现代C++学习 - 异步编程基础" << std::endl;
    
    // 演示同步方式的问题
    demo_synchronous_problem();
    
    // 演示异步执行的优势
    demo_async_basic();
    
    // 演示手动控制的异步操作
    demo_promise_future();
    
    std::cout << "\n程序结束" << std::endl;
    return 0;
}