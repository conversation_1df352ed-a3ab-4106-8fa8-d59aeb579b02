/**
 * @file test_optimized_protocol.cpp
 * @brief 测试优化后的协议处理 - 使用ASIO streambuf的分包处理
 */

#include "../include/communication_manager.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <iomanip>

using namespace ModernComm;

int main() {
    std::cout << "=== 优化后的协议处理测试 ===" << std::endl;
    std::cout << "测试ASIO streambuf的分包和半包处理能力" << std::endl;
    std::cout << "========================================" << std::endl;
    
    try {
        // 创建通讯管理器
        auto comm_manager = CommunicationFactory::create_asio_manager();
        
        // 设置包接收回调
        comm_manager->set_packet_callback([](ParsedPacket&& packet) {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            
            std::cout << "\n⏰ [" << std::put_time(std::localtime(&time_t), "%H:%M:%S") << "] ";
            std::cout << "📦 收到完整包，类型: " << static_cast<int>(packet.type) 
                      << "，大小: " << packet.data.size() << " 字节" << std::endl;
            
            // 打印包数据的前16字节
            std::cout << "  数据预览: ";
            for (size_t i = 0; i < std::min(packet.data.size(), size_t(16)); ++i) {
                printf("%02X ", packet.data[i]);
            }
            if (packet.data.size() > 16) std::cout << "...";
            std::cout << std::endl;
        });
        
        // 设置连接状态回调
        comm_manager->set_connection_callback([](bool connected) {
            if (connected) {
                std::cout << "✅ 连接成功！使用优化后的分包处理" << std::endl;
            } else {
                std::cout << "❌ 连接断开" << std::endl;
            }
        });
        
        // 设置错误回调
        comm_manager->set_error_callback([](const std::string& error_msg) {
            std::cout << "⚠️  通讯错误: " << error_msg << std::endl;
        });
        
        // 尝试连接到设备
        std::cout << "正在连接到 192.168.2.2:6666..." << std::endl;
        auto connect_future = comm_manager->connect_async("192.168.2.2", 6666);
        
        // 等待连接结果
        auto status = connect_future.wait_for(std::chrono::seconds(10));
        
        if (status == std::future_status::ready) {
            bool connected = connect_future.get();
            
            if (connected) {
                std::cout << "🎉 连接建立成功！开始测试优化后的协议处理..." << std::endl;
                
                // 监听数据30秒
                std::cout << "开始监听数据，持续30秒..." << std::endl;
                
                for (int i = 0; i < 30; ++i) {
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                    
                    // 每10秒显示一次统计信息
                    if ((i + 1) % 10 == 0) {
                        auto stats = comm_manager->get_statistics();
                        std::cout << "\n📊 [统计信息 - " << (i + 1) << "秒]" << std::endl;
                        std::cout << "接收字节数: " << stats.bytes_received << std::endl;
                        std::cout << "接收包数: " << stats.packets_received << std::endl;
                        std::cout << "发送字节数: " << stats.bytes_sent << std::endl;
                        std::cout << "发送包数: " << stats.packets_sent << std::endl;
                        std::cout << "========================================" << std::endl;
                    }
                    
                    // 检查连接状态
                    if (!comm_manager->is_connected()) {
                        std::cout << "连接已断开，退出监听" << std::endl;
                        break;
                    }
                }
                
                std::cout << "\n监听完成，断开连接..." << std::endl;
                comm_manager->disconnect();
                
            } else {
                std::cout << "❌ 连接失败" << std::endl;
            }
        } else {
            std::cout << "⏱️  连接超时（10秒）" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "\n✨ 优化后的协议处理测试完成" << std::endl;
    std::cout << "主要改进:" << std::endl;
    std::cout << "1. 使用asio::streambuf替代手动缓冲区管理" << std::endl;
    std::cout << "2. 在传输层直接处理分包和半包问题" << std::endl;
    std::cout << "3. 协议层只需处理完整的数据包" << std::endl;
    std::cout << "4. 减少了数据拷贝和缓冲区操作" << std::endl;
    
    return 0;
}
