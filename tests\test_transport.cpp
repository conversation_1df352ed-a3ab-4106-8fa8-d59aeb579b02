/**
 * @file test_transport.cpp
 * @brief 测试AsioTransport的完整功能
 */

#include "../include/asio_transport.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <string>
#include <vector>

using namespace ModernComm;

// 辅助函数：将字符串转换为字节数组
std::vector<uint8_t> string_to_bytes(const std::string& str) {
    return std::vector<uint8_t>(str.begin(), str.end());
}

// 辅助函数：将字节数组转换为字符串
std::string bytes_to_string(const std::vector<uint8_t>& bytes) {
    return std::string(bytes.begin(), bytes.end());
}

// 测试基本功能
void test_basic_functionality() {
    std::cout << "\n=== 测试基本功能 ===" << std::endl;
    
    // 创建传输对象
    AsioTransport transport;
    
    // 设置接收回调
    transport.set_receive_callback([](const std::vector<uint8_t>& data) {
        std::string message = bytes_to_string(data);
        std::cout << "收到数据: " << message << std::endl;
    });
    
    // 检查初始连接状态
    std::cout << "初始连接状态: " << (transport.is_connected() ? "已连接" : "未连接") << std::endl;
    
    // 尝试发送（应该失败，因为未连接）
    std::string test_message = "Hello, ASIO!";
    auto send_future = transport.send_async(string_to_bytes(test_message));
    bool send_result = send_future.get();
    std::cout << "未连接发送结果: " << (send_result ? "成功" : "失败") << std::endl;
    
    // 断开连接（即使未连接）
    transport.disconnect();
    
    std::cout << "基本功能测试完成" << std::endl;
}

// 测试连接到echo服务器
void test_echo_server() {
    std::cout << "\n=== 测试连接到echo服务器 ===" << std::endl;
    
    // 创建传输对象
    AsioTransport transport;
    
    // 设置接收回调
    transport.set_receive_callback([](const std::vector<uint8_t>& data) {
        std::string message = bytes_to_string(data);
        std::cout << "收到echo响应: " << message << std::endl;
    });
    
    // 连接到echo服务器
    std::cout << "连接到echo服务器..." << std::endl;
    auto connect_future = transport.connect_async("tcpbin.com", 4242);
    bool connect_result = connect_future.get();
    
    if (connect_result) {
        std::cout << "连接成功！" << std::endl;
        
        // 发送测试消息
        std::string test_message = "Hello from ModernCommLib!";
        std::cout << "发送消息: " << test_message << std::endl;
        
        auto send_future = transport.send_async(string_to_bytes(test_message));
        bool send_result = send_future.get();
        
        std::cout << "发送结果: " << (send_result ? "成功" : "失败") << std::endl;
        
        // 等待接收响应
        std::cout << "等待响应..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 断开连接
        std::cout << "断开连接..." << std::endl;
        transport.disconnect();
    } else {
        std::cout << "连接失败！" << std::endl;
    }
    
    std::cout << "Echo服务器测试完成" << std::endl;
}

// 测试错误处理
void test_error_handling() {
    std::cout << "\n=== 测试错误处理 ===" << std::endl;
    
    // 创建传输对象
    AsioTransport transport;
    
    // 尝试连接到不存在的服务器
    std::cout << "尝试连接到不存在的服务器..." << std::endl;
    auto connect_future = transport.connect_async("non-existent-server.example", 12345);
    bool connect_result = connect_future.get();
    
    std::cout << "连接结果: " << (connect_result ? "成功" : "失败") << std::endl;
    
    // 尝试连接到无效端口
    std::cout << "尝试连接到无效端口..." << std::endl;
    connect_future = transport.connect_async("localhost", 99999);  // 无效端口
    connect_result = connect_future.get();
    
    std::cout << "连接结果: " << (connect_result ? "成功" : "失败") << std::endl;
    
    std::cout << "错误处理测试完成" << std::endl;
}

int main() {
    std::cout << "ModernCommLib - AsioTransport 测试程序" << std::endl;
    
    try {
        // 测试基本功能
        test_basic_functionality();
        
        // 测试连接到echo服务器
        test_echo_server();
        
        // 测试错误处理
        test_error_handling();
        
        std::cout << "\n所有测试完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}