/**
 * @file communication_manager.cpp
 * @brief 通讯管理器实现
 */

#include "../include/communication_manager.h"
#include "../include/asio_transport.h"
#include "../include/protocol_completion_condition.h"
#include <iostream>

namespace ModernComm {

CommunicationManager::CommunicationManager(std::unique_ptr<SimpleTransport> transport)
    : transport_(transport ? std::move(transport) : std::make_unique<AsioTransport>())
    , protocol_(std::make_unique<ADMotionProtocolProcessor>())
{
    std::cout << "通讯管理器已创建" << std::endl;
    
    // 设置组件之间的连接
    setup_component_connections();
}

CommunicationManager::~CommunicationManager() {
    std::cout << "通讯管理器正在销毁" << std::endl;
    disconnect();
}

void CommunicationManager::setup_component_connections() {
    std::cout << "设置组件连接..." << std::endl;
    
    // 新架构：为传输层设置PacketFinder
    auto packet_finder = std::make_unique<PacketFinder>();

    // 将PacketFinder注入到AsioTransport中
    if (auto asio_transport = dynamic_cast<AsioTransport*>(transport_.get())) {
        asio_transport->set_packet_finder(std::move(packet_finder));
        std::cout << "PacketFinder已注入到AsioTransport" << std::endl;
    }

    // 关键连接1：传输层收到完整包后，传递给协议层处理
    transport_->set_receive_callback([this](const std::vector<uint8_t>& data) {
        std::cout << "传输层 → 协议层：收到完整包 " << data.size() << " 字节" << std::endl;
        handle_received_complete_packet(data);
    });
    
    // 关键连接2：协议层解析出包后，传递给用户回调
    protocol_->set_packet_handler([this](ParsedPacket&& packet) {
        std::cout << "协议层 → 用户：解析出包，类型=" << static_cast<int>(packet.type) << std::endl;
        handle_parsed_packet(std::move(packet));
    });
    
    // 关键连接3：协议层错误处理
    protocol_->set_error_handler([this](const std::string& error_msg) {
        std::cout << "协议层错误：" << error_msg << std::endl;
        handle_protocol_error(error_msg);
    });
    
    std::cout << "组件连接设置完成" << std::endl;
}

std::future<bool> CommunicationManager::connect_async(const std::string& host, int port) {
    std::cout << "通讯管理器：开始连接 " << host << ":" << port << std::endl;
    
    // 直接调用传输层的连接方法
    auto future = transport_->connect_async(host, port);
    
    // 可以在这里添加连接成功后的处理逻辑
    // 比如发送初始化命令等
    
    return future;
}

void CommunicationManager::disconnect() {
    std::cout << "通讯管理器：断开连接" << std::endl;
    
    if (transport_) {
        transport_->disconnect();
    }
    
    // 清空协议层缓冲区
    if (protocol_) {
        // 新架构下协议层不再需要清空缓冲区
    }
    
    // 通知用户连接已断开
    if (connection_callback_) {
        connection_callback_(false);
    }
}

bool CommunicationManager::is_connected() const {
    return transport_ ? transport_->is_connected() : false;
}

std::future<bool> CommunicationManager::send_raw_async(const std::vector<uint8_t>& data) {
    std::cout << "通讯管理器：发送原始数据 " << data.size() << " 字节" << std::endl;
    
    if (!transport_) {
        // 创建一个失败的future
        std::promise<bool> promise;
        promise.set_value(false);
        return promise.get_future();
    }
    
    // 更新统计信息
    update_send_statistics(data.size());
    
    return transport_->send_async(data);
}

std::future<bool> CommunicationManager::send_command_async(const std::vector<int16_t>& command_data) {
    std::cout << "通讯管理器：发送命令包，命令数=" << command_data.size() << std::endl;
    
    // 构建ADMotion命令包格式：@@ + 头部 + 数据 + !!
    std::vector<uint8_t> packet;
    
    // 添加包头 @@
    packet.push_back('@');
    packet.push_back('@');
    
    // 添加头部信息（简化版本）
    uint16_t flag = 0x0001;
    uint16_t array_count = static_cast<uint16_t>(command_data.size());
    uint16_t checksum = 0;  // 简化：暂时不计算校验和
    uint16_t tick = 0x1234; // 简化：固定tick值
    
    // 添加头部字段（小端序）
    packet.push_back(flag & 0xFF);
    packet.push_back((flag >> 8) & 0xFF);
    packet.push_back(array_count & 0xFF);
    packet.push_back((array_count >> 8) & 0xFF);
    packet.push_back(checksum & 0xFF);
    packet.push_back((checksum >> 8) & 0xFF);
    packet.push_back(tick & 0xFF);
    packet.push_back((tick >> 8) & 0xFF);
    
    // 添加命令数据
    for (int16_t cmd : command_data) {
        packet.push_back(cmd & 0xFF);
        packet.push_back((cmd >> 8) & 0xFF);
    }
    
    // 添加包尾 !!
    packet.push_back('!');
    packet.push_back('!');
    
    std::cout << "构建的命令包大小：" << packet.size() << " 字节" << std::endl;
    
    return send_raw_async(packet);
}

void CommunicationManager::handle_received_complete_packet(const std::vector<uint8_t>& data) {
    // 更新接收统计
    update_receive_statistics(data.size());

    // 将完整包数据传递给协议层处理
    if (protocol_) {
        protocol_->process_complete_packet(data);
    }
}

void CommunicationManager::handle_parsed_packet(ParsedPacket&& packet) {
    // 更新包统计
    stats_.packets_received++;
    
    std::cout << "收到解析后的包：类型=" << static_cast<int>(packet.type) 
              << "，数据大小=" << packet.data.size() << " 字节" << std::endl;
    
    // 调用用户回调
    if (packet_callback_) {
        packet_callback_(std::move(packet));
    }
}

void CommunicationManager::handle_protocol_error(const std::string& error_msg) {
    std::cout << "协议错误：" << error_msg << std::endl;
    
    // 调用用户错误回调
    if (error_callback_) {
        error_callback_("协议错误: " + error_msg);
    }
}

void CommunicationManager::set_packet_callback(PacketReceivedCallback callback) {
    packet_callback_ = std::move(callback);
    std::cout << "包接收回调已设置" << std::endl;
}

void CommunicationManager::set_connection_callback(ConnectionEventCallback callback) {
    connection_callback_ = std::move(callback);
    std::cout << "连接事件回调已设置" << std::endl;
}

void CommunicationManager::set_error_callback(ErrorCallback callback) {
    error_callback_ = std::move(callback);
    std::cout << "错误回调已设置" << std::endl;
}

void CommunicationManager::update_receive_statistics(size_t bytes) {
    stats_.bytes_received += bytes;
}

void CommunicationManager::update_send_statistics(size_t bytes) {
    stats_.bytes_sent += bytes;
    stats_.packets_sent++;
}

void CommunicationManager::reset_statistics() {
    stats_ = Statistics{};
    std::cout << "统计信息已重置" << std::endl;
}

// ========== 工厂方法实现 ==========

std::unique_ptr<CommunicationManager> CommunicationFactory::create_asio_manager() {
    std::cout << "创建ASIO通讯管理器" << std::endl;
    return std::make_unique<CommunicationManager>(std::make_unique<AsioTransport>());
}

std::unique_ptr<CommunicationManager> CommunicationFactory::create_test_manager() {
    std::cout << "创建测试通讯管理器" << std::endl;
    // 这里可以创建一个用于测试的mock传输层
    return std::make_unique<CommunicationManager>(nullptr);
}

} // namespace ModernComm
