cmake_minimum_required(VERSION 3.16)
project(ModernCommLib)

# 设置C++17标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译选项
if(WIN32)
    # Windows MinGW编译选项
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
    add_definitions(-D_WIN32_WINNT=0x0601)  # Windows 7+
endif()

# ASIO配置
add_definitions(-DASIO_STANDALONE)
add_definitions(-DASIO_NO_DEPRECATED)

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../asio-1.30.2/include)

# 创建静态库
add_library(ModernCommLib STATIC
    src/asio_transport.cpp
    src/protocol_processor.cpp
    src/protocol_completion_condition.cpp
    src/communication_manager.cpp
)

# 创建测试程序
add_executable(test_basic
    tests/test_basic.cpp
)

# 新增：应用层连接测试程序
add_executable(test_application_layer
    tests/test_application_layer.cpp
)

# 新增：优化后的协议处理测试程序
add_executable(test_optimized_protocol
    tests/test_optimized_protocol.cpp
)

target_link_libraries(test_basic ModernCommLib)
target_link_libraries(test_application_layer ModernCommLib)
target_link_libraries(test_optimized_protocol ModernCommLib)

# Windows需要链接网络库
if(WIN32)
    target_link_libraries(test_basic ws2_32 wsock32)
    target_link_libraries(test_application_layer ws2_32 wsock32)
    target_link_libraries(test_optimized_protocol ws2_32 wsock32)
endif()