/**
 * @file test_complete_system.cpp
 * @brief 完整系统测试 - 验证传输层、协议层、应用层的协同工作
 */

#include "../include/communication_manager.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>

using namespace ModernComm;

void test_complete_communication_flow() {
    std::cout << "\n=== 完整通讯流程测试 ===" << std::endl;
    
    // 创建通讯管理器
    auto comm_manager = CommunicationFactory::create_asio_manager();
    
    // 设置回调函数
    comm_manager->set_packet_callback([](ParsedPacket&& packet) {
        std::cout << "用户回调：收到包，类型=" << static_cast<int>(packet.type) 
                  << "，数据大小=" << packet.data.size() << " 字节" << std::endl;
        
        // 打印包数据的前几个字节
        std::cout << "包数据前8字节：";
        for (size_t i = 0; i < std::min(packet.data.size(), size_t(8)); ++i) {
            printf("%02X ", packet.data[i]);
        }
        std::cout << std::endl;
    });
    
    comm_manager->set_connection_callback([](bool connected) {
        std::cout << "连接状态变化：" << (connected ? "已连接" : "已断开") << std::endl;
    });
    
    comm_manager->set_error_callback([](const std::string& error_msg) {
        std::cout << "通讯错误：" << error_msg << std::endl;
    });
    
    std::cout << "回调函数设置完成" << std::endl;
    
    // 连接到真实的ADMotion设备
    std::cout << "尝试连接到ADMotion设备..." << std::endl;
    auto connect_future = comm_manager->connect_async("192.168.2.2", 8080);
    
    // 等待连接结果（会超时或失败）
    std::cout << "等待连接结果..." << std::endl;
    
    // 不阻塞等待，而是检查状态
    auto status = connect_future.wait_for(std::chrono::milliseconds(100));
    if (status == std::future_status::ready) {
        bool result = connect_future.get();
        std::cout << "连接结果：" << (result ? "成功" : "失败") << std::endl;
    } else {
        std::cout << "连接仍在进行中..." << std::endl;
    }
    
    // 测试发送命令（即使连接失败也可以测试包构建）
    std::cout << "测试命令包构建..." << std::endl;
    std::vector<int16_t> test_commands = {100, 200, 300, 400, 500};
    auto send_future = comm_manager->send_command_async(test_commands);
    
    // 等待发送结果
    auto send_status = send_future.wait_for(std::chrono::milliseconds(100));
    if (send_status == std::future_status::ready) {
        bool send_result = send_future.get();
        std::cout << "发送结果：" << (send_result ? "成功" : "失败") << std::endl;
    }
    
    // 显示统计信息
    auto stats = comm_manager->get_statistics();
    std::cout << "统计信息：" << std::endl;
    std::cout << "  发送字节数：" << stats.bytes_sent << std::endl;
    std::cout << "  接收字节数：" << stats.bytes_received << std::endl;
    std::cout << "  发送包数：" << stats.packets_sent << std::endl;
    std::cout << "  接收包数：" << stats.packets_received << std::endl;
    
    // 等待一段时间，让异步操作完成
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    std::cout << "断开连接..." << std::endl;
    comm_manager->disconnect();
    
    std::cout << "完整通讯流程测试完成" << std::endl;
}

void test_protocol_parsing() {
    std::cout << "\n=== 协议解析测试 ===" << std::endl;
    
    // 创建协议处理器
    ADMotionProtocolProcessor protocol;
    
    // 设置回调
    protocol.set_packet_handler([](ParsedPacket&& packet) {
        std::cout << "协议解析成功：类型=" << static_cast<int>(packet.type) 
                  << "，数据大小=" << packet.data.size() << " 字节" << std::endl;
    });
    
    protocol.set_error_handler([](const std::string& error_msg) {
        std::cout << "协议解析错误：" << error_msg << std::endl;
    });
    
    // 构造测试数据包
    
    // 1. 测试@@命令包
    std::cout << "测试@@命令包解析..." << std::endl;
    std::vector<uint8_t> command_packet = {
        '@', '@',           // 包头
        0x01, 0x00,         // flag
        0x02, 0x00,         // array_count = 2
        0x00, 0x00,         // checksum
        0x34, 0x12,         // tick
        0x64, 0x00,         // 数据1: 100
        0xC8, 0x00,         // 数据2: 200
        '!', '!'            // 包尾
    };
    protocol.process_incoming_data(command_packet);
    
    // 2. 测试##PDO包
    std::cout << "测试##PDO包解析..." << std::endl;
    std::vector<uint8_t> pdo_packet = {
        '#', '#',           // 包头
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06,  // PDO数据前6字节
        0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C,  // PDO数据后6字节
        '!', '!'            // 包尾
    };
    protocol.process_incoming_data(pdo_packet);
    
    // 3. 测试粘包情况
    std::cout << "测试粘包处理..." << std::endl;
    std::vector<uint8_t> combined_data;
    combined_data.insert(combined_data.end(), command_packet.begin(), command_packet.end());
    combined_data.insert(combined_data.end(), pdo_packet.begin(), pdo_packet.end());
    
    // 清空缓冲区后测试
    protocol.clear_buffer();
    protocol.process_incoming_data(combined_data);
    
    // 4. 测试分片数据
    std::cout << "测试分片数据处理..." << std::endl;
    protocol.clear_buffer();
    
    // 发送一半数据
    std::vector<uint8_t> half1(command_packet.begin(), command_packet.begin() + 8);
    protocol.process_incoming_data(half1);
    
    // 发送另一半数据
    std::vector<uint8_t> half2(command_packet.begin() + 8, command_packet.end());
    protocol.process_incoming_data(half2);
    
    // 显示统计信息
    auto [processed, errors] = protocol.get_statistics();
    std::cout << "协议统计：处理包数=" << processed << "，错误数=" << errors << std::endl;
    
    std::cout << "协议解析测试完成" << std::endl;
}

void test_data_flow_visualization() {
    std::cout << "\n=== 数据流可视化测试 ===" << std::endl;
    std::cout << "这个测试展示数据在各层之间的流动过程" << std::endl;
    
    // 创建通讯管理器
    auto comm_manager = CommunicationFactory::create_asio_manager();
    
    // 设置详细的回调来观察数据流
    comm_manager->set_packet_callback([](ParsedPacket&& packet) {
        std::cout << "📦 [应用层] 最终用户收到包：" 
                  << "类型=" << static_cast<int>(packet.type) 
                  << "，大小=" << packet.data.size() << " 字节" << std::endl;
    });
    
    comm_manager->set_error_callback([](const std::string& error_msg) {
        std::cout << "❌ [应用层] 错误：" << error_msg << std::endl;
    });
    
    std::cout << "数据流路径：" << std::endl;
    std::cout << "网络 → [传输层] → [协议层] → [应用层] → 用户回调" << std::endl;
    
    // 模拟一些操作
    std::cout << "模拟发送命令..." << std::endl;
    std::vector<int16_t> commands = {1000, 2000, 3000};
    auto future = comm_manager->send_command_async(commands);
    
    // 等待一下
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    std::cout << "数据流可视化测试完成" << std::endl;
}

int main() {
    std::cout << "ModernCommLib 完整系统测试" << std::endl;
    std::cout << "========================================" << std::endl;
    
    try {
        // 测试完整通讯流程
        test_complete_communication_flow();
        
        // 测试协议解析
        test_protocol_parsing();
        
        // 测试数据流可视化
        test_data_flow_visualization();
        
        std::cout << "\n========================================" << std::endl;
        std::cout << "所有测试完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}