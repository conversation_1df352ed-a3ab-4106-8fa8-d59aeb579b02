/**
 * @file test_network_diagnostic.cpp
 * @brief 网络诊断工具 - 帮助排查连接问题
 */

#include "../include/communication_manager.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace ModernComm;

void test_ping_connectivity() {
    std::cout << "\n=== 网络连通性测试 ===" << std::endl;
    
    // 使用系统ping命令测试网络连通性
    std::cout << "测试到 *********** 的网络连通性..." << std::endl;
    
    // Windows ping命令
    int result = system("ping -n 3 ***********");
    
    if (result == 0) {
        std::cout << "✅ 网络连通性正常" << std::endl;
    } else {
        std::cout << "❌ 网络不通，请检查：" << std::endl;
        std::cout << "   1. 设备IP地址是否正确" << std::endl;
        std::cout << "   2. 网络连接是否正常" << std::endl;
        std::cout << "   3. 防火墙设置" << std::endl;
    }
}

void test_port_connectivity(const std::string& host, int port) {
    std::cout << "\n=== 端口连接测试 ===" << std::endl;
    std::cout << "测试连接到 " << host << ":" << port << std::endl;
    
    auto comm_manager = CommunicationFactory::create_asio_manager();
    
    // 设置详细的连接状态回调
    comm_manager->set_connection_callback([](bool connected) {
        if (connected) {
            std::cout << "✅ 端口连接成功！" << std::endl;
        } else {
            std::cout << "❌ 端口连接失败" << std::endl;
        }
    });
    
    comm_manager->set_error_callback([](const std::string& error_msg) {
        std::cout << "连接错误: " << error_msg << std::endl;
    });
    
    // 尝试连接
    auto connect_future = comm_manager->connect_async(host, port);
    
    // 等待连接结果，增加超时时间
    std::cout << "等待连接结果（10秒超时）..." << std::endl;
    
    auto status = connect_future.wait_for(std::chrono::seconds(10));
    
    if (status == std::future_status::ready) {
        bool connected = connect_future.get();
        if (connected) {
            std::cout << "🎉 连接成功！设备在端口 " << port << " 上监听" << std::endl;
            
            // 保持连接几秒钟，看看是否有数据
            std::cout << "保持连接5秒，监听数据..." << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(5));
            
            auto stats = comm_manager->get_statistics();
            std::cout << "接收到 " << stats.bytes_received << " 字节数据" << std::endl;
            
        } else {
            std::cout << "❌ 连接失败，可能原因：" << std::endl;
            std::cout << "   1. 设备没有在端口 " << port << " 监听" << std::endl;
            std::cout << "   2. 端口被防火墙阻止" << std::endl;
            std::cout << "   3. 设备服务未启动" << std::endl;
        }
    } else {
        std::cout << "⏱️  连接超时，可能原因：" << std::endl;
        std::cout << "   1. 网络延迟过高" << std::endl;
        std::cout << "   2. 设备响应慢" << std::endl;
        std::cout << "   3. 端口不正确" << std::endl;
    }
    
    comm_manager->disconnect();
}

void test_multiple_ports(const std::string& host) {
    std::cout << "\n=== 多端口扫描测试 ===" << std::endl;
    std::cout << "扫描常用端口..." << std::endl;
    
    // 常用的工业设备端口
    std::vector<int> common_ports = {6666, 8080, 502, 1883, 4840, 44818, 10000, 9999};
    
    for (int port : common_ports) {
        std::cout << "\n测试端口 " << port << "..." << std::endl;
        
        auto comm_manager = CommunicationFactory::create_asio_manager();
        auto connect_future = comm_manager->connect_async(host, port);
        
        // 快速测试，每个端口只等待2秒
        auto status = connect_future.wait_for(std::chrono::seconds(2));
        
        if (status == std::future_status::ready) {
            bool connected = connect_future.get();
            if (connected) {
                std::cout << "✅ 端口 " << port << " 连接成功！" << std::endl;
                
                // 快速检查是否有数据
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
                auto stats = comm_manager->get_statistics();
                if (stats.bytes_received > 0) {
                    std::cout << "   📡 收到 " << stats.bytes_received << " 字节数据" << std::endl;
                }
            } else {
                std::cout << "❌ 端口 " << port << " 连接失败" << std::endl;
            }
        } else {
            std::cout << "⏱️  端口 " << port << " 连接超时" << std::endl;
        }
        
        comm_manager->disconnect();
    }
}

void show_network_info() {
    std::cout << "\n=== 网络配置信息 ===" << std::endl;
    
    std::cout << "本机网络配置:" << std::endl;
    system("ipconfig");
    
    std::cout << "\n路由表信息:" << std::endl;
    system("route print | findstr 192.168.2");
}

int main() {
    std::cout << "ModernCommLib 网络诊断工具" << std::endl;
    std::cout << "========================================" << std::endl;
    
    try {
        // 显示网络信息
        show_network_info();
        
        // 测试网络连通性
        test_ping_connectivity();
        
        // 测试特定端口
        test_port_connectivity("***********", 6666);
        
        // 扫描多个端口
        test_multiple_ports("***********");
        
        std::cout << "\n========================================" << std::endl;
        std::cout << "网络诊断完成" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "诊断过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}