/**
 * @file communication_manager.h
 * @brief 通讯管理器 - 连接传输层和协议层
 * @details 负责协调传输层和协议层，提供统一的通讯接口
 */

#ifndef COMMUNICATION_MANAGER_H
#define COMMUNICATION_MANAGER_H

#include "simple_transport.h"
#include "protocol_processor.h"
#include <memory>
#include <functional>
#include <string>
#include <future>
#include <atomic>
#include <cstdint>
#include <vector>

namespace ModernComm {

// 前向声明
class AsioTransport;
class PacketFinder;

/**
 * @brief 通讯管理器
 * 连接传输层和协议层，提供完整的通讯功能
 */
class CommunicationManager {
public:
    // 回调函数类型定义
    using PacketReceivedCallback = std::function<void(ParsedPacket&&)>;
    using ConnectionEventCallback = std::function<void(bool connected)>;
    using ErrorCallback = std::function<void(const std::string& error_msg)>;

private:
    // 核心组件
    std::unique_ptr<SimpleTransport> transport_;           // 传输层
    std::unique_ptr<ADMotionProtocolProcessor> protocol_;  // 协议层
    
    // 用户回调
    PacketReceivedCallback packet_callback_;
    ConnectionEventCallback connection_callback_;
    ErrorCallback error_callback_;
    
    // 统计信息
    struct Statistics {
        uint64_t bytes_sent = 0;
        uint64_t bytes_received = 0;
        uint64_t packets_sent = 0;
        uint64_t packets_received = 0;
        uint64_t connection_errors = 0;
    } stats_;

public:
    /**
     * @brief 构造函数
     * @param transport 传输层实现（如果为空则创建默认的AsioTransport）
     */
    explicit CommunicationManager(std::unique_ptr<SimpleTransport> transport = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~CommunicationManager();
    
    // ========== 连接管理 ==========
    /**
     * @brief 异步连接到服务器
     * @param host 主机地址
     * @param port 端口号
     * @return future对象，可以获取连接结果
     */
    std::future<bool> connect_async(const std::string& host, int port);
    
    /**
     * @brief 断开连接
     */
    void disconnect();
    
    /**
     * @brief 检查连接状态
     * @return true表示已连接
     */
    bool is_connected() const;
    
    // ========== 数据发送 ==========
    /**
     * @brief 发送原始数据
     * @param data 要发送的数据
     * @return future对象，可以获取发送结果
     */
    std::future<bool> send_raw_async(const std::vector<uint8_t>& data);
    
    /**
     * @brief 发送ADMotion命令包
     * @param command_data 命令数据
     * @return future对象，可以获取发送结果
     */
    std::future<bool> send_command_async(const std::vector<int16_t>& command_data);
    
    // ========== 回调设置 ==========
    /**
     * @brief 设置包接收回调
     * @param callback 收到解析后的包时调用
     */
    void set_packet_callback(PacketReceivedCallback callback);
    
    /**
     * @brief 设置连接事件回调
     * @param callback 连接状态变化时调用
     */
    void set_connection_callback(ConnectionEventCallback callback);
    
    /**
     * @brief 设置错误回调
     * @param callback 发生错误时调用
     */
    void set_error_callback(ErrorCallback callback);
    
    // ========== 统计信息 ==========
    /**
     * @brief 获取统计信息
     */
    Statistics get_statistics() const { return stats_; }
    
    /**
     * @brief 重置统计信息
     */
    void reset_statistics();

private:
    /**
     * @brief 初始化组件连接
     * 设置传输层和协议层之间的数据流
     */
    void setup_component_connections();
    
    /**
     * @brief 处理传输层接收到的完整数据包
     * @param data 完整的数据包
     */
    void handle_received_complete_packet(const std::vector<uint8_t>& data);
    
    /**
     * @brief 处理协议层解析出的数据包
     * @param packet 解析后的数据包
     */
    void handle_parsed_packet(ParsedPacket&& packet);
    
    /**
     * @brief 处理协议解析错误
     * @param error_msg 错误信息
     */
    void handle_protocol_error(const std::string& error_msg);
    
    /**
     * @brief 更新统计信息
     */
    void update_receive_statistics(size_t bytes);
    void update_send_statistics(size_t bytes);
};

/**
 * @brief 通讯管理器工厂
 * 提供便捷的创建方法
 */
class CommunicationFactory {
public:
    /**
     * @brief 创建基于ASIO的通讯管理器
     */
    static std::unique_ptr<CommunicationManager> create_asio_manager();
    
    /**
     * @brief 创建用于测试的通讯管理器
     */
    static std::unique_ptr<CommunicationManager> create_test_manager();
};

} // namespace ModernComm

#endif // COMMUNICATION_MANAGER_H