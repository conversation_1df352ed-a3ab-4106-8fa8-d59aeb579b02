/**
 * @file sync_connection_test.cpp
 * @brief 使用同步连接的测试，模仿原有实现
 */

#ifdef _WIN32
#define _WIN32_WINNT 0x0601
#endif

#include <asio.hpp>
#include <iostream>
#include <string>
#include <thread>
#include <atomic>

class SimpleAsioSocket {
private:
    asio::io_context io_context_;
    std::unique_ptr<asio::ip::tcp::socket> socket_;
    std::unique_ptr<std::thread> io_thread_;
    std::atomic<bool> connected_{false};
    std::atomic<bool> running_{false};
    char read_buffer_[1024];

public:
    SimpleAsioSocket() {
        socket_ = std::make_unique<asio::ip::tcp::socket>(io_context_);
    }
    
    ~SimpleAsioSocket() {
        disconnect();
        stop_io_thread();
    }
    
    bool connect(const std::string& host, int port) {
        try {
            // 创建endpoint
            asio::ip::tcp::endpoint endpoint(asio::ip::address_v4::from_string(host), port);
            
            std::cout << "尝试连接到 " << host << ":" << port << std::endl;
            
            // 同步连接，模仿原有实现
            std::error_code ec;
            socket_->connect(endpoint, ec);
            
            if (ec) {
                std::cout << "连接失败: " << ec.message() << " (错误码: " << ec.value() << ")" << std::endl;
                return false;
            }
            
            std::cout << "✅ 连接成功!" << std::endl;
            connected_.store(true);
            
            // 设置socket选项
            #ifdef _WIN32
            int timeout = 500;
            ::setsockopt(socket_->native_handle(), SOL_SOCKET, SO_SNDTIMEO, 
                       (char*)&timeout, sizeof(timeout));
            #endif
            
            // 连接成功后启动IO线程，模仿原有实现
            start_io_thread();
            
            // 开始异步读取
            start_async_read();
            
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "连接异常: " << e.what() << std::endl;
            return false;
        }
    }
    
    void disconnect() {
        connected_.store(false);
        if (socket_ && socket_->is_open()) {
            socket_->close();
        }
    }
    
    bool is_connected() const {
        return connected_.load() && socket_ && socket_->is_open();
    }

private:
    void start_io_thread() {
        running_.store(true);
        io_thread_ = std::make_unique<std::thread>([this]() {
            std::cout << "IO线程启动" << std::endl;
            while (running_.load() && connected_.load()) {
                try {
                    // 模仿原有实现：每次循环都reset和run
                    io_context_.reset();
                    io_context_.run();
                    
                    // 短暂休眠，模仿原有实现
                    std::this_thread::sleep_for(std::chrono::microseconds(100));
                } catch (const std::exception& e) {
                    std::cout << "IO线程异常: " << e.what() << std::endl;
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
            }
            std::cout << "IO线程结束" << std::endl;
        });
    }
    
    void stop_io_thread() {
        running_.store(false);
        io_context_.stop();
        
        if (io_thread_ && io_thread_->joinable()) {
            io_thread_->join();
        }
    }
    
    void start_async_read() {
        if (!is_connected()) {
            return;
        }
        
        socket_->async_read_some(
            asio::buffer(read_buffer_, sizeof(read_buffer_)),
            [this](const std::error_code& ec, size_t bytes_received) {
                handle_read(ec, bytes_received);
            }
        );
    }
    
    void handle_read(const std::error_code& ec, size_t bytes_received) {
        if (!ec) {
            // 读取成功
            std::cout << "收到 " << bytes_received << " 字节数据: ";
            for (size_t i = 0; i < std::min(bytes_received, size_t(16)); ++i) {
                printf("%02X ", (unsigned char)read_buffer_[i]);
            }
            std::cout << std::endl;
            
            // 继续读取
            if (is_connected()) {
                start_async_read();
            }
        } else {
            // 读取错误
            if (ec == asio::error::operation_aborted) {
                std::cout << "读取操作被取消" << std::endl;
            } else {
                std::cout << "读取错误: " << ec.message() << std::endl;
                connected_.store(false);
            }
        }
    }
};

int main() {
    std::cout << "同步连接测试" << std::endl;
    std::cout << "===================" << std::endl;
    
    try {
        SimpleAsioSocket socket;
        
        // 尝试连接
        if (socket.connect("192.168.2.2", 6666)) {
            std::cout << "保持连接5秒，监听数据..." << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(5));
            
            std::cout << "断开连接..." << std::endl;
            socket.disconnect();
        } else {
            std::cout << "连接失败" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "测试完成" << std::endl;
    return 0;
}