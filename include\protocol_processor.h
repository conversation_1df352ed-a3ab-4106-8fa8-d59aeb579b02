/**
 * @file protocol_processor.h
 * @brief ADMotion协议处理器
 * @details 处理ADMotion特有的数据包格式：@@、$、%%、##
 */

#ifndef PROTOCOL_PROCESSOR_H
#define PROTOCOL_PROCESSOR_H

#include <vector>
#include <functional>
#include <memory>
#include <optional>
#include <cstdint>

namespace ModernComm {

/**
 * @brief ADMotion数据包类型
 */
enum class PacketType : uint8_t {
    Command = 0,    // @@ - 运控回复上位机指令
    IO = 1,         // $$ - 运控主动上传IO状态  
    Position = 2,   // %% - 运控主动上传轴位置
    PDO = 3         // ## - 运控主动上传PDO数据
};

/**
 * @brief @@包（命令包）的头部结构
 */
struct CommandPacketHeader {
    uint8_t header[2];      // @@ 包头 (0x40 0x40)
    uint16_t flag;          // p[2] short flag 保留，暂未使用
    uint16_t array_count;   // p[3] ArrayCount 数组个数（小端序）
    uint8_t checksum;       // p[4] checksum 保留
    uint8_t tick;           // p[5] tick Command指令索引

    CommandPacketHeader() : flag(0), array_count(0), checksum(0), tick(0) {
        header[0] = 0x40;
        header[1] = 0x40;
    }
} __attribute__((packed));

/**
 * @brief 解析后的@@包数据结构
 */
struct ParsedCommandPacket {
    uint16_t flag;              // 标志字段（保留）
    uint16_t array_count;       // 数组个数
    uint8_t checksum;           // 校验和（保留）
    uint8_t tick;               // 指令索引
    std::vector<uint16_t> data; // 数据数组（每个元素2字节）

    ParsedCommandPacket() : flag(0), array_count(0), checksum(0), tick(0) {}
};

/**
 * @brief 解析后的数据包结构
 */
struct ParsedPacket {
    PacketType type;                    // 包类型
    std::vector<uint8_t> data;         // 包数据
    size_t original_size;              // 原始包大小
    
    ParsedPacket(PacketType t, std::vector<uint8_t> d, size_t size)
        : type(t), data(std::move(d)), original_size(size) {}
};

/**
 * @brief 包边界检测结果
 */
struct PacketBoundary {
    size_t start_pos;      // 包开始位置
    size_t packet_length;  // 包长度
    bool is_complete;      // 是否完整
    PacketType type;       // 包类型
};

/**
 * @brief ADMotion协议处理器
 * 负责解析ADMotion特有的数据包格式
 */
class ADMotionProtocolProcessor {
public:
    // 回调函数类型定义
    using PacketHandler = std::function<void(ParsedPacket&&)>;
    using ErrorHandler = std::function<void(const std::string& error_msg)>;

private:
    PacketHandler packet_handler_;          // 包处理回调
    ErrorHandler error_handler_;            // 错误处理回调

    // 统计信息
    uint64_t total_packets_processed_ = 0;
    uint64_t parse_errors_ = 0;

public:
    ADMotionProtocolProcessor();
    ~ADMotionProtocolProcessor() = default;
    
    /**
     * @brief 处理接收到的完整数据包
     * @details 现在接收到的data已经保证是一个完整的ADMotion包
     * @param data 完整的数据包
     */
    void process_complete_packet(const std::vector<uint8_t>& data);

    /**
     * @brief 设置包处理回调
     * @param handler 包处理函数
     */
    void set_packet_handler(PacketHandler handler);

    /**
     * @brief 设置错误处理回调
     * @param handler 错误处理函数
     */
    void set_error_handler(ErrorHandler handler);

    /**
     * @brief 获取统计信息
     */
    std::pair<uint64_t, uint64_t> get_statistics() const {
        return {total_packets_processed_, parse_errors_};
    }

private:
    /**
     * @brief 查找数据流中的包边界
     * @param data 数据缓冲区
     * @return 包边界信息，如果没有找到完整包则返回nullopt
     */
    std::optional<PacketBoundary> find_packet_boundary(const std::vector<uint8_t>& data);
    
    /**
     * @brief 解析完整的数据包
     * @param data 包含完整包的数据
     * @param boundary 包边界信息
     * @return 解析后的包，如果解析失败则返回nullopt
     */
    std::optional<ParsedPacket> parse_packet(const std::vector<uint8_t>& data,
                                            const PacketBoundary& boundary);

    /**
     * @brief 直接解析完整的数据包（新方法）
     * @details 假设传入的data已经是一个完整的ADMotion包
     * @param data 完整的数据包
     * @return 解析后的包，如果解析失败则返回nullopt
     */
    std::optional<ParsedPacket> parse_complete_packet_data(const std::vector<uint8_t>& data);

public:
    // 解析特定类型包的方法（公有，用于测试）
    std::optional<ParsedPacket> parse_command_packet(const std::vector<uint8_t>& data);
    std::optional<ParsedPacket> parse_io_packet(const std::vector<uint8_t>& data);
    std::optional<ParsedPacket> parse_position_packet(const std::vector<uint8_t>& data);
    std::optional<ParsedPacket> parse_pdo_packet(const std::vector<uint8_t>& data);
    
    // 查找特定类型包的边界
    std::optional<PacketBoundary> find_command_boundary(const std::vector<uint8_t>& data, size_t start_pos);
    std::optional<PacketBoundary> find_io_boundary(const std::vector<uint8_t>& data, size_t start_pos);
    std::optional<PacketBoundary> find_position_boundary(const std::vector<uint8_t>& data, size_t start_pos);
    std::optional<PacketBoundary> find_pdo_boundary(const std::vector<uint8_t>& data, size_t start_pos);
    
    // 工具方法
    bool check_packet_tail(const std::vector<uint8_t>& data, size_t pos);
    uint16_t calculate_checksum(const std::vector<uint8_t>& data, size_t start, size_t length);
};

} // namespace ModernComm

#endif // PROTOCOL_PROCESSOR_H