// =====================================================
// 文件: asio_transport.h (已修正)
// =====================================================
#ifndef ASIO_TRANSPORT_H
#define ASIO_TRANSPORT_H

#include "simple_transport.h"
#include "protocol_completion_condition.h"
#include <asio.hpp>
#include <thread>
#include <atomic>
#include <mutex>

namespace ModernComm {

class AsioTransport : public SimpleTransport {
private:
    // ASIO核心组件
    asio::io_context io_context_;
    
    // **修正1：将 work_guard 提升为成员变量**
    // 它的生命周期将与 AsioTransport 对象绑定
    asio::executor_work_guard<asio::io_context::executor_type> work_guard_;

    std::unique_ptr<asio::ip::tcp::socket> socket_;
    std::unique_ptr<std::thread> io_thread_;
    
    // 状态管理
    std::atomic<bool> connected_{false};
    
    // 接收相关
    std::vector<uint8_t> receive_buffer_;
    std::function<void(const std::vector<uint8_t>&)> receive_callback_;
    std::mutex callback_mutex_;

    // 协议相关
    std::unique_ptr<PacketFinder> packet_finder_;
    asio::streambuf dynamic_buffer_;  // 动态缓冲区，用于累积数据

public:
    AsioTransport();
    ~AsioTransport();
    
    // 实现接口
    std::future<bool> connect_async(const std::string& host, int port) override;
    std::future<bool> send_async(const std::vector<uint8_t>& data) override;
    void set_receive_callback(std::function<void(const std::vector<uint8_t>&)> callback) override;
    bool is_connected() const override;
    void disconnect() override;

    /**
     * @brief 设置包查找器（用于协议层注入）
     * @param finder 包查找器实例
     */
    void set_packet_finder(std::unique_ptr<PacketFinder> finder);

private:
    // 内部方法
    void start_io_thread();
    void stop_io_thread();
    void start_receive_with_packet_finder();
    void handle_packet_receive(const std::error_code& ec, size_t bytes_received);
    void process_received_data();
};

} // namespace ModernComm

#endif // ASIO_TRANSPORT_H